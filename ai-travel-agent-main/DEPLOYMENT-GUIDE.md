# 🚀 AI Travel Planner - HANA Cloud Deployment Guide

## ✅ Issues Fixed

The SQLite native module compilation issues have been resolved by:
- Moving `@cap-js/sqlite` to devDependencies (development only)
- Using `@cap-js/hana` for production deployments
- Proper MTA build configuration for Cloud Foundry

## 📋 Prerequisites

1. **Cloud Foundry CLI**: `cf --version`
2. **MTA Build Tool**: `npm install -g mbt`
3. **SAP BTP Account** with Cloud Foundry environment
4. **HANA Cloud Instance** provisioned in your subaccount
5. **Node.js 18+** installed locally

## 🔧 Local Development Setup

### For Development (with SQLite)
```bash
# Install all dependencies including SQLite for local development
npm run setup

# Deploy to local SQLite database
npm run deploy:local

# Start development server with SQLite
npm run dev
# OR
npm run test:local
```

### For Production Testing (HANA-like)
```bash
# Install production dependencies only
npm run setup:prod

# Start without SQLite dependencies (will try to connect to HANA)
NODE_ENV=production npm start
```

### Important Notes
- **Use `npm run deploy:local`** instead of `cds deploy` for local SQLite setup
- **Never use `cds deploy`** when targeting HANA Cloud deployment
- **Use `npm run dev`** for full local development with SQLite

## 🚀 Cloud Foundry Deployment Steps

### 1. Login to Cloud Foundry
```bash
cf login -a https://api.cf.sap.hana.ondemand.com
```

### 2. Target your organization and space
```bash
cf target -o YOUR_ORG -s YOUR_SPACE
```

### 3. Set Environment Variables
```bash
cf set-env ai-travel-planner GEMINI_API_KEY "your_gemini_api_key"
cf set-env ai-travel-planner OPENWEATHER_API_KEY "your_openweather_api_key"
cf set-env ai-travel-planner PEXELS_API_KEY "your_pexels_api_key"
```

### 4. Build and Deploy
```bash
# Clean build and deploy
npm run build:cf
npm run deploy:cf
```

Or use the provided scripts:
```bash
# Make scripts executable
chmod +x build-cf.sh deploy-cf.sh

# Build for Cloud Foundry
./build-cf.sh

# Deploy to Cloud Foundry
./deploy-cf.sh
```

## 🗄️ Services Created

The deployment will create these SAP BTP services:
- **HANA Cloud HDI Container**: `ai-travel-hana-db` (hdi-shared plan)
- **XSUAA Authentication**: `ai-travel-xsuaa` (application plan)
- **Destination Service**: `ai-travel-destination` (lite plan)
- **Connectivity Service**: `ai-travel-connectivity` (lite plan)

## 🔍 Verification Steps

### 1. Check Application Status
```bash
cf apps | grep ai-travel-planner
```

### 2. View Application Logs
```bash
cf logs ai-travel-planner --recent
```

### 3. Check Service Bindings
```bash
cf services
```

### 4. Test the Application
Access your application at the generated route (shown in deployment output).

## 🛠️ Troubleshooting

### SQLite Issues (Fixed)
- ✅ SQLite is now only used in development
- ✅ Production uses HANA Cloud exclusively
- ✅ No native module compilation issues

### Common Deployment Issues

1. **Service Creation Failures**
   ```bash
   # Check service status
   cf services
   
   # Recreate failed services
   cf delete-service ai-travel-hana-db
   cf create-service hana hdi-shared ai-travel-hana-db
   ```

2. **Application Start Failures**
   ```bash
   # Check logs
   cf logs ai-travel-planner --recent
   
   # Restart application
   cf restart ai-travel-planner
   ```

3. **Environment Variable Issues**
   ```bash
   # Check current environment
   cf env ai-travel-planner
   
   # Set missing variables
   cf set-env ai-travel-planner VARIABLE_NAME "value"
   cf restart ai-travel-planner
   ```

## 📁 Project Structure

```
ai-travel-agent-main/
├── db/                          # Database models and HANA artifacts
│   ├── models/schema.cds       # CDS data models
│   ├── package.json            # DB deployer configuration
│   └── src/gen/                # Generated HANA artifacts
├── srv/                        # CAP services
│   ├── travel-service.js       # Service implementation
│   └── travel-service.cds      # Service definitions
├── app/webapp/                 # UI5 frontend
├── mta.yaml                    # Multi-Target Application descriptor
├── package.json                # Main project dependencies
├── build-cf.sh                 # Build script for Cloud Foundry
├── deploy-cf.sh                # Deployment script
└── mta_archives/               # Generated MTA archive
    └── ai-travel-planner_1.0.0.mtar
```

## 🎯 Next Steps

1. **Deploy to Cloud Foundry** using the steps above
2. **Configure your HANA Cloud instance** in SAP BTP cockpit
3. **Set up authentication** with XSUAA service
4. **Test the application** with real travel planning scenarios
5. **Monitor performance** using SAP BTP monitoring tools

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Review Cloud Foundry logs: `cf logs ai-travel-planner --recent`
3. Verify service bindings: `cf services`
4. Check SAP BTP cockpit for service status
