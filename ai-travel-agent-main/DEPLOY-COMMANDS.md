# 🚀 HANA Cloud Deployment Commands

## Copy and paste these commands one by one:

### 1. Login to Cloud Foundry
```bash
cf login -a https://api.cf.sap.hana.ondemand.com
```
**Enter your SAP BTP email and password when prompted**

### 2. Check available organizations and spaces
```bash
cf orgs
cf spaces
```

### 3. Target your organization and space
```bash
cf target -o YOUR_ORG_NAME -s YOUR_SPACE_NAME
```
**Replace YOUR_ORG_NAME and YOUR_SPACE_NAME with actual values from step 2**

### 4. Set your Gemini API key (REQUIRED)
```bash
cf set-env ai-travel-planner GEMINI_API_KEY "your_actual_gemini_api_key_here"
```
**Replace with your real Gemini API key**

### 5. Set optional API keys (if you have them)
```bash
cf set-env ai-travel-planner OPENWEATHER_API_KEY "your_openweather_key"
cf set-env ai-travel-planner PEXELS_API_KEY "your_pexels_key"
```

### 6. Deploy the application
```bash
cf deploy mta_archives/ai-travel-planner_1.0.0.mtar
```

### 7. Check deployment status
```bash
cf apps
```

### 8. View application logs
```bash
cf logs ai-travel-planner --recent
```

### 9. Check services
```bash
cf services
```

## 🎯 What will be created:

1. **Application**: `ai-travel-planner-srv`
2. **Database Deployer**: `ai-travel-planner-db-deployer`
3. **HANA HDI Container**: `ai-travel-hana-db`
4. **XSUAA Service**: `ai-travel-xsuaa`
5. **Destination Service**: `ai-travel-destination`
6. **Connectivity Service**: `ai-travel-connectivity`

## ✅ Success Indicators:

- All services show status "create succeeded"
- Application shows status "running"
- You get a URL to access your application

## 🔍 Troubleshooting Commands:

If something goes wrong:
```bash
# Check app status
cf app ai-travel-planner

# View detailed logs
cf logs ai-travel-planner --recent

# Check service status
cf service ai-travel-hana-db

# Restart app if needed
cf restart ai-travel-planner
```

## 📱 Access Your Application:

After successful deployment, you'll get a URL like:
`https://ai-travel-planner-srv-[random].cfapps.sap.hana.ondemand.com`

The application will be ready to use for AI-powered travel planning! 🎉
