# 🚀 SAP HANA Cloud & Cloud Foundry Deployment Guide

## Prerequisites

1. **Cloud Foundry CLI** installed: `cf --version`
2. **MTA Build Tool** installed: `npm install -g mbt`
3. **SAP BTP Account** with Cloud Foundry environment
4. **HANA Cloud Instance** provisioned in your subaccount
5. **API Keys** for Gemini AI, OpenWeather, and Pexels
6. **Node.js 18+** installed locally

## Deployment Steps

### 1. Login to Cloud Foundry

```bash
cf login -a https://api.cf.sap.hana.ondemand.com
```

### 2. Target your organization and space

```bash
cf target -o YOUR_ORG -s YOUR_SPACE
```

### 3. Set Environment Variables

```bash
cf set-env ai-travel-planner GEMINI_API_KEY "your_gemini_api_key"
cf set-env ai-travel-planner OPENWEATHER_API_KEY "your_openweather_api_key"
cf set-env ai-travel-planner PEXELS_API_KEY "your_pexels_api_key"
```

### 4. Build and Deploy

```bash
# Make scripts executable
chmod +x build-cf.sh deploy-cf.sh

# Build for Cloud Foundry
./build-cf.sh

# Deploy to Cloud Foundry
./deploy-cf.sh
```

## Services Created

- **HANA Cloud HDI Container**: `ai-travel-hana-db` (hdi-shared plan)
- **XSUAA Authentication**: `ai-travel-xsuaa` (application plan)
- **Destination Service**: `ai-travel-destination` (lite plan)
- **Connectivity Service**: `ai-travel-connectivity` (lite plan)

## HANA-Specific Features

### Database Schema

- **HANA-optimized data types**: Decimal(15,2), Timestamp, CLOB
- **Proper constraints**: NOT NULL, DEFAULT values
- **UUID primary keys**: Compatible with HANA Cloud
- **Hierarchical data**: TravelPlans → DailyPlans → PlannedActivities

### Performance Optimizations

- **Connection pooling**: Automatic via CAP framework
- **Prepared statements**: Built-in SQL injection protection
- **Transaction management**: ACID compliance with HANA
- **Error handling**: Graceful fallbacks for different environments

## Post-Deployment

1. Access your application at the generated route
2. Check logs: `cf logs ai-travel-planner --recent`
3. Monitor app: `cf app ai-travel-planner`

## Troubleshooting

- Check service bindings: `cf services`
- View environment variables: `cf env ai-travel-planner`
- Restart app: `cf restart ai-travel-planner`
