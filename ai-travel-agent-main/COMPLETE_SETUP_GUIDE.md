# AI Travel Planner - Complete Setup Guide

## 📋 **REQUIREMENTS OVERVIEW**

### 1. **SAP BTP Account & Services**
- SAP BTP Trial or Paid Account
- Cloud Foundry Environment
- HANA Cloud Instance
- Required Service Instances:
  - XSUAA (Authentication)
  - Destination Service
  - Connectivity Service

### 2. **External API Keys (Required)**
- **Google Gemini AI API Key** (REQUIRED for AI functionality)
- OpenWeatherMap API Key (Optional - for weather data)
- Pexels API Key (Optional - for photos)
- Google Maps API Key (Optional - for maps)

### 3. **Development Tools**
- Node.js (v18 or higher)
- SAP CDS CLI
- Cloud Foundry CLI
- MBT (Multi-Target Application Build Tool)
- Git
- Code Editor (VS Code recommended)

---

## 🚀 **STEP-BY-STEP SETUP**

### **STEP 1: SAP BTP Account Setup**

#### 1.1 Create SAP BTP Account
1. Go to [SAP BTP Trial](https://account.hanatrial.ondemand.com/)
2. Sign up for free trial account
3. Verify your email and complete registration
4. Access your BTP Cockpit

#### 1.2 Set Up Cloud Foundry Environment
1. In BTP Cockpit, go to **Account Explorer**
2. Select your subaccount
3. Go to **Cloud Foundry** → **Spaces**
4. Create a new space (e.g., "dev" or "ai-travel")
5. Note down:
   - **Organization Name**
   - **Space Name**
   - **API Endpoint** (e.g., `https://api.cf.us10-001.hana.ondemand.com`)

### **STEP 2: HANA Cloud Setup**

#### 2.1 Create HANA Cloud Instance
1. In BTP Cockpit, go to **Services** → **Service Marketplace**
2. Search for "SAP HANA Cloud"
3. Click **Create** and configure:
   - **Plan**: `hana` (for trial: `hana-cloud-trial`)
   - **Instance Name**: `ai-travel-hana-db`
   - **Database Name**: `AI_TRAVEL_DB`
   - **Administrator Password**: Create a strong password (SAVE THIS!)
4. Wait for instance to be created (10-15 minutes)

#### 2.2 Configure HANA Cloud Access
1. Go to **SAP HANA Cloud Central**
2. Find your instance and click **Actions** → **Open in SAP HANA Database Explorer**
3. Test connection with your admin credentials
4. Note down connection details:
   - **Host**: (e.g., `abc123-def456.hana.trial-us10.hanacloud.ondemand.com`)
   - **Port**: `443`
   - **Database Name**: `AI_TRAVEL_DB`

### **STEP 3: Required Service Instances**

#### 3.1 Create XSUAA Service
```bash
cf create-service xsuaa application ai-travel-xsuaa
```

#### 3.2 Create Destination Service
```bash
cf create-service destination lite ai-travel-destination
```

#### 3.3 Create Connectivity Service
```bash
cf create-service connectivity lite ai-travel-connectivity
```

#### 3.4 Create HDI Container
```bash
cf create-service hana hdi-shared ai-travel-hana-db
```

### **STEP 4: External API Keys Setup**

#### 4.1 Google Gemini AI API (REQUIRED)
1. Go to [Google AI Studio](https://aistudio.google.com/)
2. Sign in with Google account
3. Click **Get API Key**
4. Create new project or select existing
5. Generate API key and **SAVE IT SECURELY**

#### 4.2 OpenWeatherMap API (Optional)
1. Go to [OpenWeatherMap](https://openweathermap.org/api)
2. Sign up for free account
3. Go to **API Keys** section
4. Copy your default API key

#### 4.3 Pexels API (Optional)
1. Go to [Pexels API](https://www.pexels.com/api/)
2. Create free account
3. Go to **Your API Key** section
4. Copy your API key

#### 4.4 Google Maps API (Optional)
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create new project or select existing
3. Enable **Maps JavaScript API**
4. Go to **Credentials** → **Create Credentials** → **API Key**
5. Copy your API key

### **STEP 5: Development Tools Installation**

#### 5.1 Install Node.js
```bash
# Download from https://nodejs.org/ (v18 or higher)
node --version  # Verify installation
npm --version   # Verify npm
```

#### 5.2 Install SAP CDS CLI
```bash
npm install -g @sap/cds-dk
cds --version  # Verify installation
```

#### 5.3 Install Cloud Foundry CLI
```bash
# Download from https://github.com/cloudfoundry/cli/releases
cf --version  # Verify installation
```

#### 5.4 Install MBT
```bash
# Download from https://github.com/SAP/cloud-mta-build-tool/releases
mbt --version  # Verify installation
```

#### 5.5 Login to Cloud Foundry
```bash
cf login -a <YOUR_API_ENDPOINT>
# Enter your BTP credentials
# Select your org and space
```

---

## 📝 **CREDENTIALS CHECKLIST**

Save these credentials securely:

### SAP BTP Credentials
- [ ] BTP Username/Email
- [ ] BTP Password
- [ ] Subaccount ID
- [ ] Organization Name
- [ ] Space Name
- [ ] API Endpoint

### HANA Cloud Credentials
- [ ] HANA Instance Name
- [ ] HANA Host URL
- [ ] Database Name
- [ ] Admin Username (usually DBADMIN)
- [ ] Admin Password

### API Keys
- [ ] Google Gemini AI API Key (REQUIRED)
- [ ] OpenWeatherMap API Key
- [ ] Pexels API Key
- [ ] Google Maps API Key

---

## 🔧 **NEXT STEPS**

Once you have all the above set up, I can help you:

1. **Recreate the Application Code** - I'll help you rebuild the entire project
2. **Configure Environment Files** - Set up all credentials and configurations
3. **Test Local Development** - Verify everything works locally
4. **Deploy to Cloud Foundry** - Complete deployment process
5. **Test Production** - Verify the deployed application

---

## 💰 **COST CONSIDERATIONS**

### Free Tier Limits
- **SAP BTP Trial**: Free for 90 days
- **HANA Cloud Trial**: 30GB storage, limited compute
- **Google Gemini AI**: Free tier with rate limits
- **OpenWeatherMap**: 1000 calls/day free
- **Pexels**: 200 requests/hour free

### Paid Options
- **SAP BTP**: Pay-as-you-go or subscription
- **HANA Cloud**: Based on compute and storage
- **Google Gemini AI**: Pay per token after free tier

---

## 🔍 **VERIFICATION COMMANDS**

### Test Your Setup
```bash
# Test CF CLI connection
cf target

# List your services
cf services

# Test CDS installation
cds --version

# Test MBT installation
mbt --version

# Test Node.js
node --version
npm --version
```

### Test API Keys
```bash
# Test Gemini AI API (replace YOUR_API_KEY)
curl -H "Content-Type: application/json" \
     -d '{"contents":[{"parts":[{"text":"Hello"}]}]}' \
     "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=YOUR_API_KEY"
```

---

## 🚨 **COMMON ISSUES & SOLUTIONS**

### Issue 1: HANA Cloud Connection Failed
**Solution**:
- Check if HANA instance is running
- Verify firewall settings allow port 443
- Ensure correct host URL format

### Issue 2: CF Login Failed
**Solution**:
- Verify API endpoint URL
- Check BTP account status
- Ensure correct org/space names

### Issue 3: Service Creation Failed
**Solution**:
- Check quota limits in your space
- Verify service availability in your region
- Try different service plan

### Issue 4: API Key Not Working
**Solution**:
- Verify API key is active
- Check rate limits
- Ensure correct API endpoint

---

## 📋 **QUICK START CHECKLIST**

- [ ] SAP BTP account created and verified
- [ ] Cloud Foundry environment set up
- [ ] HANA Cloud instance created and running
- [ ] All required services created
- [ ] Google Gemini AI API key obtained
- [ ] Development tools installed
- [ ] CF CLI logged in and targeting correct space
- [ ] All credentials saved securely

---

## ❓ **NEED HELP?**

I'm ready to help you with:

1. **🏗️ Setting up SAP BTP account** - Step-by-step BTP configuration
2. **🗄️ Creating HANA Cloud instance** - Database setup and configuration
3. **🔑 Getting API keys** - Help with external service registrations
4. **💻 Installing development tools** - Local environment setup
5. **📝 Recreating the application** - Complete code reconstruction
6. **🚀 Deployment process** - End-to-end deployment guide

**Just tell me which step you'd like to start with, and I'll provide detailed, hands-on guidance!**

---

## 🎯 **RECOMMENDED ORDER**

1. **Start with SAP BTP account** (if you don't have one)
2. **Get Google Gemini AI API key** (required for core functionality)
3. **Set up development tools** (for local development)
4. **Create HANA Cloud instance** (can take 10-15 minutes)
5. **Create required services** (quick setup)
6. **Recreate application code** (I'll help you build it)
7. **Test locally** (verify everything works)
8. **Deploy to cloud** (final step)
