namespace travel.india;

using {cuid} from '@sap/cds/common';

// HANA-specific annotations for better performance
annotate travel.india.TravelPlans with @(
    title      : 'Travel Plans',
    description: 'Main travel plan records'
) {
    ID           @title: 'Plan ID';
    title        @title: 'Plan Title';
    destinations @title: 'Destinations';
    startDate    @title: 'Start Date';
    endDate      @title: 'End Date';
    budget       @title: 'Budget';
    travelers    @title: 'Number of Travelers';
};

//////////////////////////////////////////////////////
// 🌏 Master Data Entities
//////////////////////////////////////////////////////

entity States : cuid {
    name            : String(50);
    code            : String(5);
    description     : String(255);
    region          : String(30);
    popularityScore : Integer;
    avgRating       : Decimal(2, 1);
}

entity Cities : cuid {
    name            : String(50);
    state           : Association to States;
    description     : String(255);
    famousFor       : String(255);
    avgCostPerDay   : Decimal(10, 2);
    bestSeason      : String(50);
    popularityScore : Integer;
    avgRating       : Decimal(2, 1);
}

entity Attractions : cuid {
    name            : String(100);
    city            : Association to Cities;
    type            : String(50); // e.g. Temple, Beach, Fort
    description     : String(255);
    entryFee        : Decimal(10, 2);
    openHours       : String(50);
    avgVisitTime    : Integer; // in minutes
    popularityScore : Integer;
    avgRating       : Decimal(2, 1);
}

//////////////////////////////////////////////////////
// 👤 User Related Entities
//////////////////////////////////////////////////////

entity Users : cuid {
    username      : String(50);
    email         : String(100);
    passwordHash  : String(255);
    favoriteTrips : Composition of many TravelPlans
                        on favoriteTrips.user = $self;
}

entity UserPreferences : cuid {
    user           : Association to Users;
    preferredStyle : String(30); // budget, comfort, luxury
    interests      : String(255); // beaches, food, culture, etc.
    budgetRange    : String(50);
}

//////////////////////////////////////////////////////
// 🏝️ Travel Plan Entities
//////////////////////////////////////////////////////

entity TravelPlans : cuid {
    user           : Association to Users;
    title          : String(100) not null;
    description    : String(255);
    destinations   : String(255) not null;
    startDate      : Date not null;
    endDate        : Date not null;
    duration       : String(50); // Changed to String for HANA compatibility
    budget         : Decimal(15, 2) not null; // Increased precision for HANA
    travelers      : Integer not null;
    preferences    : String(500);
    geminiResponse : LargeString; // HANA supports CLOB for large text
    status         : String(20) not null default 'Draft';
    createdAt      : Timestamp; // HANA timestamp
    modifiedAt     : Timestamp; // HANA timestamp

    dailyPlans     : Composition of many DailyPlans
                         on dailyPlans.travelPlan = $self;
}

entity DailyPlans : cuid {
    travelPlan  : Association to TravelPlans not null;
    day         : Integer not null;
    date        : Date not null;
    city        : String(100) not null;
    theme       : String(100);
    description : String(255);
    budget      : Decimal(12, 2) default 0.0; // HANA decimal with default
    createdAt   : Timestamp; // HANA timestamp
    modifiedAt  : Timestamp; // HANA timestamp

    activities  : Composition of many PlannedActivities
                      on activities.dailyPlan = $self;
}

entity PlannedActivities : cuid {
    dailyPlan  : Association to DailyPlans not null;
    startTime  : String(20) not null;
    activity   : String(200) not null;
    location   : String(100);
    type       : String(50);
    cost       : Decimal(12, 2) default 0.0; // HANA decimal with default
    priority   : String(30) default 'Medium';
    tips       : String(500);
    createdAt  : Timestamp; // HANA timestamp
    modifiedAt : Timestamp; // HANA timestamp
}

//////////////////////////////////////////////////////
// 💰 Additional Entities
//////////////////////////////////////////////////////

entity Budgets : cuid {
    travelPlan : Association to TravelPlans;
    category   : String(50);
    amount     : Decimal(12, 2);
}

entity WeatherCache : cuid {
    city        : Association to Cities;
    date        : Date;
    weatherInfo : String(255);
    lastUpdated : Timestamp;
}
