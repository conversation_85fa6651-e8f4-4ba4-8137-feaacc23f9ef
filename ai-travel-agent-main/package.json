{"name": "ai-travel-agent", "version": "1.0.0", "description": "<PERSON><PERSON>t-AI Travel Agent - Intelligent Travel Planning for India", "main": "srv/travel-service.js", "scripts": {"start": "cds-serve", "start:prod": "NODE_ENV=production cds-serve", "watch": "cds watch", "build": "cds build", "deploy:local": "cds deploy", "deploy:hana": "cds deploy --to hana", "setup": "npm install --include=dev", "setup:prod": "npm install --production", "build:cf": "npm run setup:prod && npm run build && mbt build", "deploy:cf": "cf deploy mta_archives/ai-travel-planner_1.0.0.mtar", "dev": "npm run deploy:local && cds watch", "test:local": "NODE_ENV=development cds watch"}, "dependencies": {"@cap-js/hana": "^1.0.0", "@google/generative-ai": "^0.2.0", "@sap/cds": "^8.0.0", "@sap/xssec": "^3.6.0", "@sap/hdi-deploy": "^4.0.0", "axios": "^1.11.0", "dotenv": "^17.2.1"}, "devDependencies": {"@cap-js/db-service": "^1.0.0", "@cap-js/sqlite": "^1.0.0"}, "cds": {"requires": {"db": {"[development]": {"kind": "sqlite", "credentials": {"url": "db.sqlite"}}, "[production]": {"kind": "hana-cloud"}}, "auth": {"[production]": {"kind": "xsuaa"}}}, "serve": {"index": "app/index.html", "static": [{"path": "/", "src": "app"}]}, "hana": {"deploy-format": "hdbtable"}, "build": {"target": "gen", "tasks": [{"for": "hana", "dest": "db"}, {"for": "node-cf", "dest": "srv"}]}}}