using { travel.india as db } from '../db/models/schema';

service TravelService @(path: '/service/plan') {

    // Expose database entities for CRUD operations
    entity TravelPlans as projection on db.TravelPlans;
    entity DailyPlans as projection on db.DailyPlans;
    entity PlannedActivities as projection on db.PlannedActivities;
    entity Users as projection on db.Users;
    entity States as projection on db.States;
    entity Cities as projection on db.Cities;
    entity Attractions as projection on db.Attractions;

    // Define a structured type for the plan response
    type GeneratedPlan {
        planJson : LargeString;
        planId : String;
    }

    // Health check response type
    type HealthStatus {
        status: String;
        database: String;
        timestamp: String;
    }

    // Health check function for Cloud Foundry
    function health() returns HealthStatus;

    // Update the function to return this new type
    function generatePlan(
        origin: String,
        destination: String,
        startDate: String,
        endDate: String,
        budget: Integer,
        travelers: Integer,
        travelStyle: String
    ) returns GeneratedPlan;
}