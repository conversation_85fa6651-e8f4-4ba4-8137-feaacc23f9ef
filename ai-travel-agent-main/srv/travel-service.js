const cds = require('@sap/cds');
const { GoogleGenerativeAI } = require('@google/generative-ai');
const axios = require('axios');

// Load environment variables
if (process.env.NODE_ENV !== 'production') {
    require('dotenv').config();
}

// HANA Database connection helper
async function getHanaConnection() {
    try {
        // In Cloud Foundry, CDS automatically handles HANA connection via service bindings
        if (process.env.VCAP_SERVICES) {
            console.log("🔗 Using HANA Cloud service binding");
            return cds.db;
        }

        // For local development with HANA
        if (process.env.CDS_ENV === 'production' || process.env.NODE_ENV === 'production') {
            console.log("🔗 Connecting to HANA database");
            return cds.db;
        }

        // Fallback to SQLite for local development
        console.log("🔗 Using SQLite for local development");
        return cds.db;
    } catch (error) {
        console.error("❌ Database connection error:", error);
        throw error;
    }
}

async function getWeatherForBlocks(destination, date, apiKey) {
    if (!apiKey) {
        console.warn("OPENWEATHER_API_KEY not configured, using default weather");
        return getDefaultWeather();
    }

    try {
        // Configure axios with timeout and proper headers
        const axiosConfig = {
            timeout: 10000, // 10 second timeout
            headers: {
                'User-Agent': 'Travel-Agent/1.0'
            }
        };

        // Use HTTPS for OpenWeatherMap API with India-specific search
        // Handle popular destinations with specific city names
        let searchQuery = destination;
        const popularDestinations = {
            'goa': 'Panaji,Goa,IN',
            'kerala': 'Kochi,Kerala,IN',
            'rajasthan': 'Jaipur,Rajasthan,IN',
            'manali': 'Manali,Himachal Pradesh,IN',
            'shimla': 'Shimla,Himachal Pradesh,IN',
            'mumbai': 'Mumbai,Maharashtra,IN',
            'delhi': 'New Delhi,Delhi,IN',
            'bangalore': 'Bangalore,Karnataka,IN',
            'chennai': 'Chennai,Tamil Nadu,IN'
        };

        if (popularDestinations[destination.toLowerCase()]) {
            searchQuery = popularDestinations[destination.toLowerCase()];
        } else {
            searchQuery = `${destination},IN`;
        }

        const geoUrl = `https://api.openweathermap.org/geo/1.0/direct?q=${encodeURIComponent(searchQuery)}&limit=5&appid=${apiKey}`;
        console.log(`Fetching coordinates for: ${searchQuery}`);

        const geoRes = await axios.get(geoUrl, axiosConfig);

        if (!geoRes.data || !geoRes.data.length) {
            console.warn(`No coordinates found for destination: ${destination}`);
            return getDefaultWeather();
        }

        // Find the best result in India (country code 'IN')
        let location = geoRes.data.find(loc => loc.country === 'IN');

        // For specific destinations, prefer the correct state
        if (destination.toLowerCase() === 'goa') {
            location = geoRes.data.find(loc => loc.country === 'IN' && loc.state === 'Goa') || location;
        }

        // Fallback to first result if no India location found
        location = location || geoRes.data[0];
        const { lat, lon } = location;
        console.log(`Found coordinates: ${lat}, ${lon} for ${destination}`);

        // Fetch weather forecast
        const forecastUrl = `https://api.openweathermap.org/data/2.5/forecast?lat=${lat}&lon=${lon}&appid=${apiKey}&units=metric`;
        const forecastRes = await axios.get(forecastUrl, axiosConfig);

        if (!forecastRes.data || !forecastRes.data.list) {
            console.warn("Invalid weather forecast response");
            return getDefaultWeather();
        }

        const blocks = { Morning: 9, Afternoon: 15, Evening: 19 };
        let results = {};

        // Get all forecasts for the specific date
        const dayForecasts = forecastRes.data.list.filter(fc => fc.dt_txt.startsWith(date));

        if (dayForecasts.length === 0) {
            // If no forecasts for exact date, use closest available forecasts
            console.log(`No exact weather data for ${date}, using closest available`);
            const allForecasts = forecastRes.data.list;
            for (const [period, hour] of Object.entries(blocks)) {
                // Find closest forecast to desired hour
                let closest = allForecasts.reduce((prev, curr) => {
                    const currHour = parseInt(curr.dt_txt.split(' ')[1].slice(0, 2));
                    const prevHour = parseInt(prev.dt_txt.split(' ')[1].slice(0, 2));
                    return Math.abs(currHour - hour) < Math.abs(prevHour - hour) ? curr : prev;
                });

                if (closest && closest.weather && closest.weather[0]) {
                    results[period] = {
                        desc: closest.weather[0].description,
                        icon: closest.weather[0].icon,
                        icon_url: `https://openweathermap.org/img/wn/${closest.weather[0].icon}@2x.png`,
                        temp: Math.round(closest.main.temp)
                    };
                } else {
                    results[period] = getDefaultWeather();
                }
            }
        } else {
            // Use exact date forecasts
            for (const [period, hour] of Object.entries(blocks)) {
                let found = dayForecasts.find(fc => {
                    const fcHour = parseInt(fc.dt_txt.split(' ')[1].slice(0, 2));
                    return Math.abs(fcHour - hour) < 3;
                }) || dayForecasts[0]; // Fallback to first forecast of the day

                if (found && found.weather && found.weather[0]) {
                    results[period] = {
                        desc: found.weather[0].description,
                        icon: found.weather[0].icon,
                        icon_url: `https://openweathermap.org/img/wn/${found.weather[0].icon}@2x.png`,
                        temp: Math.round(found.main.temp)
                    };
                } else {
                    results[period] = getDefaultWeather();
                }
            }
        }

        console.log(`Weather data fetched successfully for ${destination} on ${date}`);
        return results;

    } catch (error) {
        console.error(`Weather fetch failed for ${destination}:`, error.message);
        if (error.code === 'ECONNABORTED') {
            console.error("Weather API request timed out");
        } else if (error.response) {
            console.error(`Weather API error: ${error.response.status} - ${error.response.statusText}`);
        } else if (error.request) {
            console.error("No response received from weather API");
        }
        return getDefaultWeather();
    }
}

// Default weather fallback
function getDefaultWeather() {
    return {
        desc: "partly cloudy",
        icon: "02d",
        icon_url: "https://openweathermap.org/img/wn/<EMAIL>",
        temp: "28"
    };
}

function addDays(start, offset) {
    const d = new Date(start);
    d.setDate(d.getDate() + offset);
    return d.toISOString().slice(0, 10);
}

// Enhanced photo fetching with Pexels API
async function fetchPexelsPhotos(placeName, apiKey, destination) {
    if (!apiKey) {
        console.warn("PEXELS_API_KEY not configured, using fallback photos");
        return getDestinationSpecificPhoto(placeName, destination);
    }

    try {
        // Try destination-specific search first
        const searchQuery = `${placeName} ${destination} India`;

        const response = await axios.get(`https://api.pexels.com/v1/search`, {
            headers: {
                'Authorization': apiKey
            },
            params: {
                query: searchQuery,
                per_page: 3,
                orientation: 'landscape'
            }
        });

        if (response.data.photos && response.data.photos.length > 0) {
            const photo = response.data.photos[0];
            return {
                url: photo.src.medium,
                photographer: photo.photographer,
                photographer_url: photo.photographer_url
            };
        }

        // Try broader search without destination
        const broadResponse = await axios.get(`https://api.pexels.com/v1/search`, {
            headers: {
                'Authorization': apiKey
            },
            params: {
                query: placeName,
                per_page: 3,
                orientation: 'landscape'
            }
        });

        if (broadResponse.data.photos && broadResponse.data.photos.length > 0) {
            const photo = broadResponse.data.photos[0];
            return {
                url: photo.src.medium,
                photographer: photo.photographer,
                photographer_url: photo.photographer_url
            };
        }

        // Fallback to destination-specific photo
        return getDestinationSpecificPhoto(placeName, destination);
    } catch (error) {
        console.error(`Failed to fetch photo for ${placeName}:`, error.message);
        return getDestinationSpecificPhoto(placeName, destination);
    }
}

// Destination-specific photo mapping
function getDestinationSpecificPhoto(placeName, destination) {
    const lowerPlace = placeName.toLowerCase();
    const lowerDest = destination ? destination.toLowerCase() : '';

    // Simplified fallback photos for major destinations
    const fallbackPhotos = {
        'goa': 'https://images.unsplash.com/photo-1512343879784-a960bf40e7f2?w=400',
        'manali': 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400',
        'kerala': 'https://images.unsplash.com/photo-1602216056096-3b40cc0c9944?w=400',
        'rajasthan': 'https://images.unsplash.com/photo-1518548419970-58e3b4079ab2?w=400'
    };

    // Food fallback
    if (lowerPlace.includes('food') || lowerPlace.includes('dish') || lowerPlace.includes('cuisine')) {
        return { url: 'https://images.unsplash.com/photo-1596797038530-2c107229654b?w=400' };
    }

    // Return destination-specific or generic fallback
    return { url: fallbackPhotos[lowerDest] || 'https://images.unsplash.com/photo-1564507592333-c60657eea523?w=400' };
}

// Function to generate results HTML
function generateResultsHTML(travelPlan) {
    return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Your AI Travel Plan - Atithi</title>
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                min-height: 100vh;
                line-height: 1.6;
            }
            .container {
                max-width: 1200px;
                margin: 0 auto;
                padding: 20px;
            }
            .header {
                text-align: center;
                margin-bottom: 40px;
                padding: 30px 0;
            }
            .header h1 {
                font-size: 2.5em;
                margin-bottom: 15px;
                text-shadow: 3px 3px 6px rgba(0,0,0,0.4);
                background: linear-gradient(45deg, #ffd700, #ffed4e);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
            }
            .back-btn {
                display: inline-block;
                background: rgba(255,255,255,0.2);
                color: white;
                padding: 12px 25px;
                border-radius: 25px;
                text-decoration: none;
                margin-bottom: 30px;
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255,255,255,0.3);
                transition: all 0.3s ease;
            }
            .back-btn:hover {
                background: rgba(255,255,255,0.3);
                transform: translateY(-2px);
            }
            .trip-summary {
                background: rgba(255,255,255,0.15);
                padding: 30px;
                border-radius: 20px;
                margin-bottom: 40px;
                backdrop-filter: blur(15px);
                border: 1px solid rgba(255,255,255,0.2);
                box-shadow: 0 8px 32px rgba(0,0,0,0.2);
                text-align: center;
            }
            .trip-summary h2 {
                color: #ffd700;
                font-size: 2em;
                margin-bottom: 20px;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            }
            .summary-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 20px;
                margin-top: 25px;
            }
            .summary-item {
                background: rgba(0,0,0,0.2);
                padding: 15px;
                border-radius: 10px;
                border-left: 4px solid #ffd700;
            }
            .summary-item strong {
                color: #ffd700;
                display: block;
                margin-bottom: 5px;
            }
            .day-card {
                background: rgba(255,255,255,0.15);
                margin-bottom: 30px;
                border-radius: 20px;
                overflow: hidden;
                backdrop-filter: blur(15px);
                border: 1px solid rgba(255,255,255,0.2);
                box-shadow: 0 8px 32px rgba(0,0,0,0.2);
            }
            .day-header {
                background: linear-gradient(45deg, #ffd700, #ffed4e);
                color: #333;
                padding: 20px 30px;
                font-weight: 700;
                font-size: 1.3em;
            }
            .day-content {
                padding: 30px;
            }
            .activity {
                background: rgba(0,0,0,0.2);
                margin-bottom: 20px;
                padding: 20px;
                border-radius: 15px;
                border-left: 4px solid #87ceeb;
            }
            .activity-time {
                color: #ffd700;
                font-weight: 600;
                font-size: 1.1em;
                margin-bottom: 10px;
            }
            .activity-title {
                font-size: 1.2em;
                font-weight: 600;
                margin-bottom: 8px;
                color: #fff;
            }
            .activity-location {
                color: #87ceeb;
                margin-bottom: 10px;
                font-style: italic;
            }
            .activity-description {
                opacity: 0.9;
                margin-bottom: 10px;
                line-height: 1.5;
            }
            .activity-cost {
                color: #90EE90;
                font-weight: 600;
            }
            .section {
                background: rgba(255,255,255,0.15);
                padding: 30px;
                border-radius: 20px;
                margin-bottom: 30px;
                backdrop-filter: blur(15px);
                border: 1px solid rgba(255,255,255,0.2);
                box-shadow: 0 8px 32px rgba(0,0,0,0.2);
            }
            .section h3 {
                color: #ffd700;
                font-size: 1.8em;
                margin-bottom: 25px;
                text-align: center;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            }
            .grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 20px;
            }
            .card {
                background: rgba(0,0,0,0.2);
                padding: 20px;
                border-radius: 15px;
                border: 1px solid rgba(255,255,255,0.1);
            }
            .card h4 {
                color: #87ceeb;
                margin-bottom: 10px;
                font-size: 1.2em;
            }
            .card p {
                opacity: 0.9;
                margin-bottom: 8px;
            }
            .price {
                color: #90EE90;
                font-weight: 600;
                font-size: 1.1em;
            }
            @media (max-width: 768px) {
                .container { padding: 15px; }
                .header h1 { font-size: 2em; }
                .grid { grid-template-columns: 1fr; }
                .summary-grid { grid-template-columns: 1fr; }
                .day-content { padding: 20px; }
                .section { padding: 20px; }
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <a href="/" class="back-btn">← Back to Home</a>
                <h1>🎉 Your Perfect Travel Plan</h1>
                <p>Generated by AI • Customized for You</p>
            </div>

            <div class="trip-summary">
                <h2>${travelPlan.trip_summary?.title || 'Your Amazing Trip'}</h2>
                <p>${travelPlan.trip_summary?.destination || ''}</p>
                <div class="summary-grid">
                    <div class="summary-item">
                        <strong>Duration</strong>
                        ${travelPlan.trip_summary?.duration || ''}
                    </div>
                    <div class="summary-item">
                        <strong>Budget</strong>
                        ₹${travelPlan.trip_summary?.total_budget?.toLocaleString() || 'N/A'}
                    </div>
                    <div class="summary-item">
                        <strong>Best Time</strong>
                        ${travelPlan.trip_summary?.best_time_to_visit || 'Year-round'}
                    </div>
                </div>
            </div>

            ${generateItinerary(travelPlan.itinerary)}
            ${generateMustVisitPlaces(travelPlan.must_visit_places)}
            ${generateMustTryFoods(travelPlan.must_try_foods)}

            <div style="text-align: center; margin: 50px 0; padding: 30px; background: rgba(255,255,255,0.1); border-radius: 20px;">
                <h3 style="color: #ffd700; margin-bottom: 15px;">Ready for Your Adventure? 🎒</h3>
                <p style="font-size: 1.1em; margin-bottom: 20px;">Your personalized travel plan is ready!</p>
                <a href="/" class="back-btn" style="display: inline-block; margin-top: 10px;">Plan Another Trip</a>
            </div>
        </div>
    </body>
    </html>
    `;
}

// Helper functions for generating HTML sections
function generateItinerary(itinerary) {
    if (!itinerary || !itinerary.length) return '';

    return itinerary.map(day => `
        <div class="day-card">
            <div class="day-header">
                📅 Day ${day.day} - ${day.date}
                <span style="font-weight: normal; font-size: 0.9em;">• ${day.theme || ''}</span>
            </div>
            <div class="day-content">
                ${day.daily_summary ? `<p style="margin-bottom: 25px; font-size: 1.1em; opacity: 0.95;">${day.daily_summary}</p>` : ''}
                ${day.activities ? day.activities.map(activity => `
                    <div class="activity">
                        <div class="activity-time">
                            ${activity.time_of_day}
                            ${activity.weather ? `<span style="display: inline-block; background: rgba(135,206,235,0.2); padding: 8px 12px; border-radius: 20px; margin-left: 10px; font-size: 0.9em;">🌤️ ${activity.weather.temp}°C • ${activity.weather.desc}</span>` : ''}
                        </div>
                        <div class="activity-title">${activity.activity}</div>
                        <div class="activity-location">📍 ${activity.location}</div>
                        <div class="activity-description">${activity.description}</div>
                        ${activity.cost ? `<div class="activity-cost">💰 Cost: ₹${activity.cost}</div>` : ''}
                        ${activity.pro_tip ? `<div style="background: rgba(255,215,0,0.1); padding: 10px; border-radius: 8px; margin-top: 10px; border-left: 3px solid #ffd700; font-size: 0.95em;">💡 <strong>Pro Tip:</strong> ${activity.pro_tip}</div>` : ''}
                    </div>
                `).join('') : ''}
            </div>
        </div>
    `).join('');
}

function generateMustVisitPlaces(places) {
    if (!places || !places.length) return '';

    return `
    <div class="section">
        <h3>🏛️ Must-Visit Places</h3>
        <div class="grid">
            ${places.map(place => `
                <div class="card">
                    <h4>${place.name}</h4>
                    <p><strong>Type:</strong> ${place.type}</p>
                    <p>${place.description}</p>
                    <p><strong>Location:</strong> ${place.location}</p>
                    <p><strong>Best Time:</strong> ${place.best_time_to_visit}</p>
                    <p><strong>Duration:</strong> ${place.estimated_time}</p>
                    ${place.entry_fee ? `<p class="price">Entry Fee: ₹${place.entry_fee}</p>` : ''}
                    ${place.tips ? `<p style="margin-top: 10px; font-style: italic;">💡 ${place.tips}</p>` : ''}
                </div>
            `).join('')}
        </div>
    </div>
    `;
}

function generateMustTryFoods(foods) {
    if (!foods || !foods.length) return '';

    return `
    <div class="section">
        <h3>🍽️ Must-Try Foods</h3>
        <div class="grid">
            ${foods.map(food => `
                <div class="card">
                    <h4>${food.name}</h4>
                    <p>${food.description}</p>
                    <p><strong>Where to try:</strong> ${food.where}</p>
                    ${food.cost ? `<p class="price">Approx Cost: ₹${food.cost}</p>` : ''}
                </div>
            `).join('')}
        </div>
    </div>
    `;
}

module.exports = cds.service.impl(async function () {





    // Health check endpoint for Cloud Foundry
    this.on('health', async (req) => {
        try {
            // Test database connection with environment-specific SQL
            const db = await cds.connect.to('db');

            // Use different SQL based on database type
            if (process.env.VCAP_SERVICES) {
                // HANA Cloud (when deployed to Cloud Foundry)
                await db.run('SELECT 1 FROM DUMMY');
            } else {
                // SQLite for local development
                await db.run('SELECT 1');
            }

            return {
                status: 'healthy',
                database: 'connected',
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            req.error(500, `Health check failed: ${error.message}`);
        }
    });

    this.on('generatePlan', async (req) => {
        try {
            const { origin, destination, startDate, endDate, budget, travelers, travelStyle } = req.data;
            if (!process.env.GEMINI_API_KEY) {
                req.error(500, "GEMINI_API_KEY is not configured.");
                return;
            }
            if (!process.env.OPENWEATHER_API_KEY) {
                console.warn("OPENWEATHER_API_KEY is not configured. Weather data will not be available.");
            }

            // Calculate days (exclusive end date for user selection)
            const numDays = Math.ceil((new Date(endDate) - new Date(startDate)) / (1000 * 60 * 60 * 24));

            // AI will generate realistic pricing, no need for manual calculation

            const prompt = `
You are Atithi, an expert travel planner for India. Generate a detailed travel itinerary in valid JSON format.

**Trip Details:**
- Origin: ${origin}
- Destination: ${destination}
- Dates: ${startDate} to ${endDate} (${numDays} days)
- Budget: ₹${budget} for ${travelers} traveler(s)
- Style: ${travelStyle}

**CRITICAL: Return ONLY valid JSON with realistic current market prices for ${destination}:**

\`\`\`json
{
  "trip_summary": {
    "title": "A ${travelStyle} Trip to ${destination}",
    "destination": "${destination}",
    "duration": "${numDays} Days",
    "total_budget": ${budget},
    "best_time_to_visit": "Provide a brief note on the best season to visit."
  },
  "itinerary": [
    {
      "day": 1,
      "date": "YYYY-MM-DD",
      "theme": "e.g., Arrival & Acclimatization",
      "daily_summary": "A brief summary of the day's plan.",
      "activities": [
        {
          "time_of_day": "Morning",
          "activity": "Detailed morning activity name",
          "location": "Specific location with full address",
          "type": "e.g., Sightseeing, Adventure, Dining, Relaxation",
          "description": "A compelling 1-2 sentence description of the morning activity.",
          "cost": 0,
          "pro_tip": "A helpful tip for this morning activity."
        },
        {
          "time_of_day": "Afternoon",
          "activity": "Detailed afternoon activity name",
          "location": "Specific location with full address",
          "type": "e.g., Sightseeing, Adventure, Dining, Relaxation",
          "description": "A compelling 1-2 sentence description of the afternoon activity.",
          "cost": 0,
          "pro_tip": "A helpful tip for this afternoon activity."
        },
        {
          "time_of_day": "Evening",
          "activity": "Detailed evening activity name",
          "location": "Specific location with full address",
          "type": "e.g., Sightseeing, Adventure, Dining, Relaxation",
          "description": "A compelling 1-2 sentence description of the evening activity.",
          "cost": 0,
          "pro_tip": "A helpful tip for this evening activity."
        }
      ]
    }
  ],
  "recommended_accommodation_options": [
    {
      "name": "REAL Hotel Name in ${destination}",
      "type": "e.g., Luxury Hotel, Budget Hostel, Boutique Guesthouse",
      "estimated_price_per_night": 0,
      "address": "Full REAL address of the accommodation in ${destination}",
      "reason": "Why is this a good fit for the user's travel style and budget?"
    },
    {
      "name": "REAL Mid-range Hotel in ${destination}",
      "type": "Business Hotel",
      "estimated_price_per_night": 0,
      "address": "Full REAL address in ${destination}",
      "reason": "Great value for money in ${destination}"
    },
    {
      "name": "REAL Boutique Stay in ${destination}",
      "type": "Heritage Hotel",
      "estimated_price_per_night": 0,
      "address": "Full REAL address in ${destination}",
      "reason": "Unique ${destination} experience"
    }
  ],
  "all_accommodation_options": [
    {
      "name": "REAL Budget Hostel in ${destination}",
      "type": "Hostel",
      "estimated_price_per_night": 0,
      "address": "REAL address in ${destination}",
      "reason": "Budget-friendly option in ${destination}"
    },
    {
      "name": "REAL Economy Hotel in ${destination}",
      "type": "Budget Hotel",
      "estimated_price_per_night": 0,
      "address": "REAL address in ${destination}",
      "reason": "Clean and comfortable"
    },
    {
      "name": "REAL Guest House in ${destination}",
      "type": "Guest House",
      "estimated_price_per_night": 0,
      "address": "REAL address in ${destination}",
      "reason": "Homely atmosphere"
    },
    {
      "name": "REAL Resort in ${destination}",
      "type": "Resort",
      "estimated_price_per_night": 0,
      "address": "REAL resort location in ${destination}",
      "reason": "Luxury experience"
    },
    {
      "name": "REAL Service Apartment in ${destination}",
      "type": "Service Apartment",
      "estimated_price_per_night": 0,
      "address": "REAL address in ${destination}",
      "reason": "Self-catering facilities"
    },
    {
      "name": "REAL Homestay in ${destination}",
      "type": "Homestay",
      "estimated_price_per_night": 0,
      "address": "REAL local area in ${destination}",
      "reason": "Authentic local experience"
    },
    {
      "name": "REAL Luxury Villa in ${destination}",
      "type": "Private Villa",
      "estimated_price_per_night": 0,
      "address": "REAL exclusive area in ${destination}",
      "reason": "Privacy and luxury"
    },
    {
      "name": "REAL Eco Lodge near ${destination}",
      "type": "Eco Resort",
      "estimated_price_per_night": 0,
      "address": "REAL nature location near ${destination}",
      "reason": "Sustainable tourism"
    }
  ],
  "must_try_foods": [
    {
        "name": "REAL famous dish 1 from ${destination}",
        "description": "Authentic description of this ${destination} specialty",
        "where": "REAL restaurant or area in ${destination}",
        "cost": 0
    },
    {
        "name": "REAL famous dish 2 from ${destination}",
        "description": "Another authentic ${destination} specialty",
        "where": "REAL restaurant or area in ${destination}",
        "cost": 0
    },
    {
        "name": "REAL famous dish 3 from ${destination}",
        "description": "Traditional ${destination} delicacy",
        "where": "REAL restaurant or area in ${destination}",
        "cost": 0
    },
    {
        "name": "REAL famous dish 4 from ${destination}",
        "description": "Must-try ${destination} sweet/dessert",
        "where": "REAL restaurant or area in ${destination}",
        "cost": 0
    }
  ],
  "recommended_restaurants": [
    {
        "name": "REAL Restaurant 1 in ${destination}",
        "type": "Local Cuisine",
        "city": "${destination}",
        "address": "REAL address in ${destination}",
        "recommended_for": "Authentic ${destination} cuisine",
        "cost": 0
    },
    {
        "name": "REAL Restaurant 2 in ${destination}",
        "type": "Multi-cuisine",
        "city": "${destination}",
        "address": "REAL address in ${destination}",
        "recommended_for": "Popular among tourists",
        "cost": 0
    },
    {
        "name": "REAL Restaurant 3 in ${destination}",
        "type": "Traditional",
        "city": "${destination}",
        "address": "REAL address in ${destination}",
        "recommended_for": "Authentic local experience",
        "cost": 0
    },
    {
        "name": "REAL Restaurant 4 in ${destination}",
        "type": "Fine Dining",
        "city": "${destination}",
        "address": "REAL address in ${destination}",
        "recommended_for": "Upscale dining experience",
        "cost": 0
    },
    {
        "name": "REAL Restaurant 5 in ${destination}",
        "type": "Street Food",
        "city": "${destination}",
        "address": "REAL street food area in ${destination}",
        "recommended_for": "Best street food in ${destination}",
        "cost": 0
    }
  ],
  "transportation_options": [
    {
      "mode": "Flight",
      "provider": "IndiGo/Air India",
      "route": "${origin} to ${destination}",
      "estimated_cost": 0,
      "duration": "2-4 hours",
      "booking_url": "https://www.makemytrip.com",
      "description": "Fastest option for long distances",
      "best_for": "Time-conscious travelers with higher budget"
    },
    {
      "mode": "Train",
      "provider": "Indian Railways",
      "route": "${origin} to ${destination}",
      "estimated_cost": 0,
      "duration": "8-24 hours",
      "booking_url": "https://www.irctc.co.in",
      "description": "Comfortable and economical",
      "best_for": "Budget travelers who enjoy scenic routes"
    },
    {
      "mode": "Bus",
      "provider": "State Transport/Private",
      "route": "${origin} to ${destination}",
      "estimated_cost": 0,
      "duration": "6-18 hours",
      "booking_url": "https://www.redbus.in",
      "description": "Most economical option",
      "best_for": "Budget-conscious travelers"
    },
    {
      "mode": "Car/Taxi",
      "provider": "Ola/Uber/Local",
      "route": "${origin} to ${destination}",
      "estimated_cost": 0,
      "duration": "4-12 hours",
      "booking_url": "https://www.olacabs.com",
      "description": "Door-to-door convenience",
      "best_for": "Families and groups wanting flexibility"
    }
  ],
  "must_visit_places": [
    {
      "name": "REAL famous place 1 in ${destination}",
      "type": "Historical Monument/Beach/Temple/etc",
      "description": "Detailed description of why this ${destination} place is special",
      "location": "REAL address in ${destination}",
      "best_time_to_visit": "Early morning/Sunset/etc",
      "entry_fee": 50,
      "estimated_time": "2-3 hours",
      "tips": "Helpful tips for visiting this ${destination} place"
    },
    {
      "name": "REAL famous place 2 in ${destination}",
      "type": "Natural/Cultural attraction",
      "description": "Why this ${destination} attraction is must-visit",
      "location": "REAL address in ${destination}",
      "best_time_to_visit": "Best time to visit",
      "entry_fee": 100,
      "estimated_time": "Half day",
      "tips": "Practical tips for ${destination} visitors"
    },
    {
      "name": "REAL famous place 3 in ${destination}",
      "type": "Adventure/Nature spot",
      "description": "What makes this ${destination} place unique",
      "location": "REAL address in ${destination}",
      "best_time_to_visit": "Optimal visiting time",
      "entry_fee": 0,
      "estimated_time": "3-4 hours",
      "tips": "Essential tips for ${destination}"
    },
    {
      "name": "REAL famous place 4 in ${destination}",
      "type": "Cultural/Shopping area",
      "description": "Cultural significance of this ${destination} place",
      "location": "REAL address in ${destination}",
      "best_time_to_visit": "Best visiting hours",
      "entry_fee": 25,
      "estimated_time": "2 hours",
      "tips": "Insider tips for ${destination}"
    },
    {
      "name": "REAL famous place 5 in ${destination}",
      "type": "Scenic/Adventure spot",
      "description": "Why this ${destination} spot is unmissable",
      "location": "REAL address in ${destination}",
      "best_time_to_visit": "Perfect timing",
      "entry_fee": 75,
      "estimated_time": "4-5 hours",
      "tips": "Pro tips for ${destination} visitors"
    }
  ],
  "overall_tips": [
    "💡 REAL tip about traveling in ${destination} - safety advice",
    "💡 REAL tip about ${destination} culture and customs",
    "💡 REAL tip about transportation in ${destination}",
    "💡 REAL tip about food and dining in ${destination}",
    "💡 REAL tip about shopping in ${destination}",
    "💡 REAL tip about weather in ${destination}",
    "💡 REAL tip about local customs in ${destination}",
    "💡 REAL tip about money and payments in ${destination}",
    "💡 REAL tip about language in ${destination}",
    "💡 REAL tip about accommodation in ${destination}",
    "💡 REAL tip about photography in ${destination}",
    "💡 REAL tip about emergency contacts in ${destination}"
  ],
  "expense_breakdown": {
    "accommodation": {
      "total": 0,
      "per_night": 0,
      "nights": ${numDays}
    },
    "transportation": {
      "total": 0,
      "details": "Train/bus costs for ${origin} to ${destination}"
    },
    "food": {
      "total": 0,
      "per_day": 0
    },
    "activities": {
      "total": ${Math.round(budget * 0.15)},
      "details": "Sightseeing, tours, entry fees"
    },
    "miscellaneous": {
      "total": ${Math.round(budget * 0.1)},
      "details": "Shopping, tips, emergency fund"
    },
    "total_estimated_cost": ${budget}
  }
}
\`\`\`

**Important Rules:**
1. The final output must be ONLY the JSON object, enclosed in \`\`\`json ... \`\`\`.
2. Ensure the 'itinerary' array contains exactly ${numDays} elements (not more, not less).
3. CRITICAL: Each day MUST have exactly 3 activities - Morning, Afternoon, and Evening. No exceptions.
4. Provide exactly 3 accommodation options in 'recommended_accommodation_options' and 8 options in 'all_accommodation_options'.
5. Include exactly 4 must-try foods and 5 recommended restaurants.
6. Include exactly 5 must-visit places.
7. Include at least 12 comprehensive travel tips in 'overall_tips'.
8. Use REAL place names, restaurant names, and addresses specific to ${destination}.
9. Use realistic estimated pricing based on current market rates for ${destination}.
10. Transportation costs should be realistic estimates for ${origin} to ${destination}, not budget-based calculations.
11. CRITICAL: Return ONLY valid JSON. No markdown formatting, no explanations.
12. Ensure all strings are properly quoted and valid JSON syntax.
            `;

            const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
            const model = genAI.getGenerativeModel({ model: "gemini-2.5-pro" });
            const result = await model.generateContent(prompt);
            const text = result.response.text();
            let jsonString = text;

            // Extract JSON from markdown-wrapped block or plain
            const jsonStartIndex = text.indexOf('```json');
            if (jsonStartIndex !== -1) {
                const jsonEndIndex = text.lastIndexOf('```');
                if (jsonEndIndex > jsonStartIndex) {
                    jsonString = text.substring(jsonStartIndex + 7, jsonEndIndex).trim();
                }
            } else {
                const firstBrace = text.indexOf('{');
                const lastBrace = text.lastIndexOf('}');
                if (firstBrace !== -1 && lastBrace > firstBrace) {
                    jsonString = text.substring(firstBrace, lastBrace + 1);
                }
            }

            // Clean up common JSON formatting issues
            jsonString = jsonString
                .replace(/,(\s*[}\]])/g, '$1')  // Remove trailing commas
                .replace(/([{,]\s*)(\w+):/g, '$1"$2":')  // Quote unquoted keys
                .replace(/:\s*'([^']*)'/g, ': "$1"')  // Replace single quotes with double quotes
                .replace(/\n\s*\n/g, '\n')  // Remove empty lines
                .trim();

            let travelPlan;
            try {
                travelPlan = JSON.parse(jsonString);
            } catch (parseError) {
                console.error("JSON Parse Error:", parseError.message);
                console.error("JSON String length:", jsonString.length);
                console.error("Error position:", parseError.message.match(/position (\d+)/)?.[1]);

                // Log the problematic area around the error position
                const errorPos = parseInt(parseError.message.match(/position (\d+)/)?.[1] || "0");
                const start = Math.max(0, errorPos - 100);
                const end = Math.min(jsonString.length, errorPos + 100);
                console.error("JSON around error position:", jsonString.substring(start, end));

                // Use basic fallback plan
                console.log("Creating basic fallback travel plan due to JSON parse error");
                travelPlan = {
                    trip_summary: { title: `Trip to ${destination}`, destination, duration: `${numDays} Days` },
                    itinerary: [],
                    must_visit_places: [],
                    must_try_foods: [],
                    recommended_restaurants: [],
                    transportation_options: [],
                    recommended_accommodation_options: [],
                    all_accommodation_options: [],
                    overall_tips: [`Visit ${destination} and enjoy your trip!`],
                    expense_breakdown: { total_estimated_cost: budget }
                };
            }

            // NORMALIZE ACTIVITIES ARRAY for each day - ENSURE 3 ACTIVITIES PER DAY
            if (Array.isArray(travelPlan.itinerary)) {
                travelPlan.itinerary.forEach((day) => {
                    // Migrate morning/afternoon/evening keys if present
                    if (!day.activities && (day.morning || day.afternoon || day.evening)) {
                        day.activities = [];
                        if (day.morning) day.activities.push(day.morning);
                        if (day.afternoon) day.activities.push(day.afternoon);
                        if (day.evening) day.activities.push(day.evening);
                        delete day.morning; delete day.afternoon; delete day.evening;
                    }

                    // Ensure activities array exists
                    if (!Array.isArray(day.activities)) {
                        day.activities = [];
                    }

                    // Ensure we have exactly 3 activities (Morning, Afternoon, Evening)
                    const timeSlots = ["Morning", "Afternoon", "Evening"];
                    const existingSlots = day.activities.map(act => act.time_of_day);

                    timeSlots.forEach(slot => {
                        if (!existingSlots.includes(slot)) {
                            day.activities.push({
                                time_of_day: slot,
                                activity: `${slot} exploration in ${destination}`,
                                location: destination,
                                type: "Sightseeing",
                                description: `Enjoy ${slot.toLowerCase()} activities in ${destination}`,
                                cost: slot === "Morning" ? 150 : slot === "Afternoon" ? 300 : 100,
                                pro_tip: `Make the most of your ${slot.toLowerCase()} in ${destination}`
                            });
                        }
                    });

                    // Sort activities by time of day
                    day.activities.sort((a, b) => {
                        const order = { "Morning": 0, "Afternoon": 1, "Evening": 2 };
                        return order[a.time_of_day] - order[b.time_of_day];
                    });

                    // Ensure we have exactly 3 activities
                    if (day.activities.length > 3) {
                        day.activities = day.activities.slice(0, 3);
                    }
                });
            }

            // Enforce EXACT numDays in the itinerary
            if (Array.isArray(travelPlan.itinerary) && travelPlan.itinerary.length > numDays) {
                travelPlan.itinerary = travelPlan.itinerary.slice(0, numDays);
            }
            if (Array.isArray(travelPlan.itinerary) && travelPlan.itinerary.length < numDays) {
                let last = travelPlan.itinerary[travelPlan.itinerary.length - 1];
                while (travelPlan.itinerary.length < numDays) {
                    let dayCopy = Object.assign({}, last, {
                        day: travelPlan.itinerary.length + 1,
                        date: addDays(startDate, travelPlan.itinerary.length)
                    });
                    travelPlan.itinerary.push(dayCopy);
                }
            }

            // Weather injection with improved error handling
            for (let i = 0; i < numDays; ++i) {
                if (!travelPlan.itinerary || !travelPlan.itinerary[i]) continue;
                const day = travelPlan.itinerary[i];
                day.date = addDays(startDate, i);

                if (Array.isArray(day.activities)) {
                    const blockWeather = await getWeatherForBlocks(destination, day.date, process.env.OPENWEATHER_API_KEY);
                    const fallbackWeather = getDefaultWeather();

                    for (let act of day.activities) {
                        let slotKey = act.time_of_day ? (
                            act.time_of_day.charAt(0).toUpperCase() + act.time_of_day.slice(1).toLowerCase()
                        ) : "Morning";

                        // Use weather data if available, otherwise use fallback
                        let wx = blockWeather[slotKey] || Object.values(blockWeather)[0] || fallbackWeather;
                        act.weather = wx;
                    }
                }
            }

            // Enhanced photo fetching for must_visit_places using Pexels API
            if (travelPlan.must_visit_places && Array.isArray(travelPlan.must_visit_places)) {
                for (let place of travelPlan.must_visit_places) {
                    const photo = await fetchPexelsPhotos(place.name, process.env.PEXELS_API_KEY, destination);
                    if (photo) {
                        place.photo = photo;
                    }
                }
            }

            // Enhanced photo fetching for must_try_foods using Pexels API
            if (travelPlan.must_try_foods && Array.isArray(travelPlan.must_try_foods)) {
                for (let food of travelPlan.must_try_foods) {
                    // Use food name + destination for better search results
                    const searchQuery = `${food.name} food`;
                    const photo = await fetchPexelsPhotos(searchQuery, process.env.PEXELS_API_KEY, destination);
                    if (photo) {
                        food.photo = photo;
                    }
                }
            }

            // Handle backward compatibility: convert must_try_places to must_visit_places
            if (travelPlan.must_try_places && !travelPlan.must_visit_places) {
                travelPlan.must_visit_places = travelPlan.must_try_places;
            }

            // Add realistic transportation options if missing
            if (!travelPlan.transportation_options) {
                travelPlan.transportation_options = [
                    {
                        mode: "Flight",
                        provider: "IndiGo/Air India",
                        route: `${origin} to ${destination}`,
                        estimated_cost: 10000,
                        duration: "2-4 hours",
                        booking_url: "https://www.makemytrip.com",
                        description: "Fastest option for long distances",
                        best_for: "Time-conscious travelers with higher budget"
                    },
                    {
                        mode: "Train",
                        provider: "Indian Railways",
                        route: `${origin} to ${destination}`,
                        estimated_cost: 1500,
                        duration: "8-24 hours",
                        booking_url: "https://www.irctc.co.in",
                        description: "Comfortable and economical",
                        best_for: "Budget travelers who enjoy scenic routes"
                    },
                    {
                        mode: "Bus",
                        provider: "State Transport/Private",
                        route: `${origin} to ${destination}`,
                        estimated_cost: 800,
                        duration: "6-18 hours",
                        booking_url: "https://www.redbus.in",
                        description: "Most economical option",
                        best_for: "Budget-conscious travelers"
                    },
                    {
                        mode: "Car/Taxi",
                        provider: "Ola/Uber/Local",
                        route: `${origin} to ${destination}`,
                        estimated_cost: 6000,
                        duration: "4-12 hours",
                        booking_url: "https://www.olacabs.com",
                        description: "Door-to-door convenience",
                        best_for: "Families and groups wanting flexibility"
                    }
                ];
            }

            // Save the travel plan to database - HANA enabled
            console.log("💾 Saving travel plan to database...");
            const planId = await saveTravelPlanToDatabase(travelPlan, { origin, destination, startDate, endDate, budget, travelers, travelStyle });
            console.log("✅ Travel plan saved successfully with ID:", planId);

            return {
                planJson: JSON.stringify(travelPlan),
                planId: planId || "generated-plan"
            };
        } catch (error) {
            console.error("ERROR generating plan:", error.message, error.stack);
            req.error(500, "Failed to generate plan: " + error.message);
        }
    });

    // Function to save travel plan to database - HANA Compatible
    async function saveTravelPlanToDatabase(travelPlan, requestParams) {
        try {
            const { origin, destination, startDate, endDate, budget, travelers, travelStyle } = requestParams;

            // Get database connection (HANA or SQLite)
            const db = await getHanaConnection();

            // Create main travel plan record with HANA-compatible data types
            const planData = {
                ID: cds.utils.uuid(), // Generate UUID for HANA
                title: travelPlan.trip_summary?.title || `Trip to ${destination}`,
                description: travelPlan.trip_summary?.destination || `${travelers} traveler(s) trip to ${destination}`,
                destinations: destination,
                startDate: startDate,
                endDate: endDate,
                duration: travelPlan.trip_summary?.duration || `${Math.ceil((new Date(endDate) - new Date(startDate)) / (1000 * 60 * 60 * 24))} days`,
                budget: parseFloat(budget), // Ensure decimal type for HANA
                travelers: parseInt(travelers), // Ensure integer type
                preferences: travelStyle,
                geminiResponse: JSON.stringify(travelPlan),
                status: 'Generated',
                createdAt: new Date().toISOString(), // HANA timestamp
                modifiedAt: new Date().toISOString()
            };

            // Insert travel plan - HANA compatible with proper error handling
            let result;
            try {
                result = await cds.db.run(
                    INSERT.into('TravelService.TravelPlans').entries(planData)
                );
            } catch (dbError) {
                console.error("❌ Database insert error:", dbError);
                // Fallback: try with different table name format for different environments
                try {
                    result = await cds.db.run(
                        INSERT.into('TravelService_TravelPlans').entries(planData)
                    );
                } catch (fallbackError) {
                    console.error("❌ Fallback insert also failed:", fallbackError);
                    throw fallbackError;
                }
            }

            // HANA returns different result structure than SQLite
            const planId = planData.ID; // Use the UUID we generated
            console.log("📝 Travel plan saved with ID:", planId);

            // Save daily plans if available - HANA compatible
            if (travelPlan.itinerary && Array.isArray(travelPlan.itinerary)) {
                for (const dayPlan of travelPlan.itinerary) {
                    const dailyPlanId = cds.utils.uuid(); // Generate UUID for HANA
                    const dailyPlanData = {
                        ID: dailyPlanId,
                        travelPlan_ID: planId,
                        day: parseInt(dayPlan.day) || 1,
                        date: dayPlan.date,
                        city: destination,
                        theme: dayPlan.theme || `Day ${dayPlan.day}`,
                        description: dayPlan.daily_summary || `Activities for day ${dayPlan.day}`,
                        budget: 0.0, // Decimal type for HANA
                        createdAt: new Date().toISOString(),
                        modifiedAt: new Date().toISOString()
                    };

                    try {
                        await cds.db.run(
                            INSERT.into('TravelService.DailyPlans').entries(dailyPlanData)
                        );
                    } catch (dbError) {
                        // Fallback for different table naming
                        await cds.db.run(
                            INSERT.into('TravelService_DailyPlans').entries(dailyPlanData)
                        );
                    }

                    // Save activities for this day - HANA compatible
                    if (dayPlan.activities && Array.isArray(dayPlan.activities)) {
                        for (const activity of dayPlan.activities) {
                            const activityData = {
                                ID: cds.utils.uuid(), // Generate UUID for HANA
                                dailyPlan_ID: dailyPlanId,
                                startTime: activity.time_of_day || 'Morning',
                                activity: activity.activity || 'Activity',
                                location: activity.location || destination,
                                type: activity.type || 'General',
                                cost: parseFloat(activity.cost) || 0.0, // Decimal type for HANA
                                priority: 'Medium',
                                tips: activity.pro_tip || activity.description || '',
                                createdAt: new Date().toISOString(),
                                modifiedAt: new Date().toISOString()
                            };

                            try {
                                await cds.db.run(
                                    INSERT.into('TravelService.PlannedActivities').entries(activityData)
                                );
                            } catch (dbError) {
                                // Fallback for different table naming
                                await cds.db.run(
                                    INSERT.into('TravelService_PlannedActivities').entries(activityData)
                                );
                            }
                        }
                    }
                }
            }

            return planId;
        } catch (error) {
            console.error("❌ Error saving travel plan to database:", error);
            // Don't throw error - just log it so the plan generation still succeeds
            return null;
        }
    }
});






