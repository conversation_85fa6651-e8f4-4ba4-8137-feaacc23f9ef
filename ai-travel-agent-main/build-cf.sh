#!/bin/bash

echo "🚀 Building AI Travel Planner for SAP HANA Cloud & Cloud Foundry..."

# Check prerequisites
echo "🔍 Checking prerequisites..."
if ! command -v cf &> /dev/null; then
    echo "❌ Cloud Foundry CLI not found. Please install it first."
    exit 1
fi

if ! command -v mbt &> /dev/null; then
    echo "❌ MTA Build Tool not found. Installing..."
    npm install -g mbt
fi

# Clean previous builds
echo "🧹 Cleaning previous builds..."
rm -rf gen/
rm -rf mta_archives/
rm -rf node_modules/
rm -rf package-lock.json

# Install dependencies (production only, no SQLite)
echo "📦 Installing dependencies..."
npm install --production

# Install HANA-specific dependencies for production
echo "🗄️ Installing HANA dependencies..."
npm install @cap-js/hana @sap/hdi-deploy @sap/xssec --save

# Build for production with HANA support
echo "🔨 Building for HANA Cloud production..."
export NODE_ENV=production
export CDS_ENV=production
cds build --production

# Verify HANA artifacts
echo "✅ Verifying HANA database artifacts..."
if [ -d "gen/db/src" ]; then
    echo "✅ HANA HDI artifacts generated successfully"
else
    echo "❌ HANA HDI artifacts not found"
    exit 1
fi

# Build MTA archive
echo "📦 Building MTA archive for Cloud Foundry..."
mbt build

echo "✅ Build completed! MTA archive created in mta_archives/"
echo "🗄️ HANA Cloud database artifacts ready"
echo "📁 Ready for deployment to Cloud Foundry"
