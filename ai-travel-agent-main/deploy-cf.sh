#!/bin/bash

echo "🚀 Deploying AI Travel Planner to Cloud Foundry..."

# Check if logged in to CF
if ! cf target > /dev/null 2>&1; then
    echo "❌ Not logged in to Cloud Foundry. Please run 'cf login' first."
    exit 1
fi

echo "📍 Current CF target:"
cf target

# Deploy using MTA
echo "🚀 Deploying MTA archive..."
cf deploy mta_archives/ai-travel-planner_1.0.0.mtar

echo "✅ Deployment completed!"
echo "🌐 Your application should be available at the route specified in manifest.yml"

# Show app status
echo "📊 Application status:"
cf apps | grep ai-travel-planner
