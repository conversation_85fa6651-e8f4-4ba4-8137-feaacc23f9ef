{"_version": "1.12.0", "sap.app": {"id": "travelapp", "type": "application", "i18n": "i18n/i18n.properties", "title": "{{appTitle}}", "description": "{{appDescription}}", "applicationVersion": {"version": "1.0.0"}, "dataSources": {"mainService": {"uri": "/service/plan/", "type": "OData", "settings": {"odataVersion": "4.0"}}}}, "sap.ui": {"technology": "UI5", "deviceTypes": {"desktop": true, "tablet": true, "phone": true}}, "sap.ui5": {"rootView": {"viewName": "travelapp.view.App", "type": "XML", "id": "app"}, "dependencies": {"minUI5Version": "1.76.0", "libs": {"sap.ui.core": {}, "sap.m": {}, "sap.ui.layout": {}, "sap.f": {}}}, "resources": {"css": [{"uri": "css/style.css"}]}, "models": {"i18n": {"type": "sap.ui.model.resource.ResourceModel", "settings": {"bundleName": "travelapp.i18n.i18n"}}, "": {"dataSource": "mainService", "preload": true, "type": "sap.ui.model.odata.v4.ODataModel", "settings": {"synchronizationMode": "None", "operationMode": "Server", "autoExpandSelect": true, "earlyRequests": true, "groupId": "$direct"}}, "ResultsModel": {"type": "sap.ui.model.json.JSONModel", "settings": {}}}, "routing": {"config": {"routerClass": "sap.m.routing.Router", "viewType": "XML", "viewPath": "travelapp.view", "controlId": "app", "controlAggregation": "pages", "async": true}, "routes": [{"pattern": "", "name": "main", "target": "main"}, {"pattern": "results", "name": "results", "target": "results"}], "targets": {"main": {"viewName": "Main", "viewLevel": 1, "viewId": "main"}, "results": {"viewName": "Results", "viewLevel": 2, "viewId": "results"}}}, "contentDensities": {"compact": true, "cozy": true}}}