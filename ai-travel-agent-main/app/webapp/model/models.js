sap.ui.define([
    "sap/ui/model/json/JSONModel",
    "sap/ui/Device"
], function (JSONModel, Device) {
    "use strict";

    return {

        createDeviceModel: function () {
            var oModel = new JSONModel(Device);
            oModel.setDefaultBindingMode("OneWay");
            return oModel;
        },

        createResultsModel: function () {
            var oModel = new JSONModel({
                trip_summary: {
                    title: "",
                    destination: "",
                    total_budget: "",
                    duration: "",
                    best_time: ""
                },
                daily_itinerary: [],
                budget_breakdown: {
                    accommodation_total: "",
                    transportation_total: "",
                    meals_total: "",
                    activities_total: "",
                    miscellaneous: "",
                    total_estimated: "",
                    budget_status: ""
                }
            });
            oModel.setDefaultBindingMode("TwoWay");
            return oModel;
        }

    };
}); 