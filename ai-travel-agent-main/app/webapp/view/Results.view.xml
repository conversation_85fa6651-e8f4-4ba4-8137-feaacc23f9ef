<mvc:View
    controllerName="travelapp.controller.Results"
    xmlns:mvc="sap.ui.core.mvc"
    xmlns:core="sap.ui.core"
    xmlns="sap.m">

    <Page title="{ResultsModel>/trip_summary/title}" showNavButton="true" navButtonPress=".onNavBack" class="resultsPage">
        <IconTabBar class="sapUiResponsiveContentPadding" expandable="false" headerBackgroundDesign="Transparent">
            <items>
                <!-- ITINERARY TAB -->
                <IconTabFilter text="Itinerary" icon="sap-icon://calendar">
                    <VBox>
                        <List items="{path: 'ResultsModel>/itinerary', templateShareable: false}" noDataText="No daily itinerary provided.">
                            <CustomListItem>
                                <Panel headerText="Day {ResultsModel>day}: {ResultsModel>theme}" expandable="true" expanded="true" class="sapUiTinyMarginBottom">
                                    <VBox class="sapUiSmallMargin">
                                        <HBox class="sapUiTinyMarginBottom">
                                            <Text text="📅 {ResultsModel>date}" class="sapUiSmallMarginEnd sapMTextBold" wrapping="true"/>
                                        </HBox>
                                        <Text text="{ResultsModel>daily_summary}" class="sapUiSmallMarginBottom" wrapping="true"/>
                                        <List items="{path: 'ResultsModel>activities', templateShareable: false}" noDataText="No activities for this day.">
                                            <CustomListItem>
                                                <VBox class="sapUiSmallMargin">
                                                    <HBox alignItems="Center" class="sapUiTinyMarginBottom">
                                                        <Text text="🕐 {ResultsModel>time_of_day}" class="sapMTextBold sapUiTinyMarginEnd"/>
                                                        <core:HTML content="&lt;img src='{ResultsModel>weather/icon_url}' alt='{ResultsModel>weather/desc}' title='{ResultsModel>weather/desc}' style='width:24px;height:24px;margin-right:8px;' /&gt;" visible="{= !!${ResultsModel>weather/icon_url} }" />
                                                        <Text text="{ResultsModel>weather/desc}, {ResultsModel>weather/temp}°C" visible="{= !!${ResultsModel>weather/desc} }"/>
                                                    </HBox>
                                                    <Text text="🎯 {ResultsModel>activity}" class="sapMTextBold sapUiTinyMarginBottom" wrapping="true"/>
                                                    <Text text="📍 {ResultsModel>location}" visible="{= !!${ResultsModel>location} }" class="sapUiTinyMarginBottom" wrapping="true"/>
                                                    <Text text="🏷️ {ResultsModel>type}" visible="{= !!${ResultsModel>type} }" class="sapUiTinyMarginBottom" wrapping="true"/>
                                                    <Text text="{ResultsModel>description}" class="sapUiSmallMarginBottom" wrapping="true"/>
                                                    <Text text="Pro Tip: {ResultsModel>pro_tip}" visible="{= !!${ResultsModel>pro_tip} }" class="sapUiTinyMarginBottom" wrapping="true"/>
                                                    <Text text="💰 Cost: ₹{ResultsModel>cost}" visible="{= !!${ResultsModel>cost} }" class="sapUiTinyMarginBottom"/>
                                                </VBox>
                                            </CustomListItem>
                                        </List>
                                    </VBox>
                                </Panel>
                            </CustomListItem>
                        </List>
                    </VBox>
                </IconTabFilter>

                <!-- RECOMMENDED STAYS TAB (Card Style) -->
                <IconTabFilter text="Recommended Stays" icon="sap-icon://bed">
                    <VBox class="sapUiMediumMargin">
                        <List items="{path: 'ResultsModel>/recommended_accommodation_options', templateShareable: false}" noDataText="No recommendations found.">
                            <CustomListItem>
                                <VBox class="card sapUiSmallMargin">
                                    <HBox alignItems="Start">
                                        <core:Icon src="sap-icon://bed" class="sapUiTinyMarginEnd sapUiTinyMarginTop"/>
                                        <VBox>
                                            <Text text="🏨 {ResultsModel>name}" class="sapMTextBold sapUiSmallMarginEnd" wrapping="true"/>
                                            <Text text="🏷️ {ResultsModel>type}" wrapping="true"/>
                                            <Text text="📍 {ResultsModel>address}" visible="{= !!${ResultsModel>address} }" wrapping="true"/>
                                            <Text text="💰 ₹{ResultsModel>estimated_price_per_night} per night" wrapping="true"/>
                                        </VBox>
                                    </HBox>
                                    <Text text="✨ {ResultsModel>reason}" class="card-reason sapUiSmallMarginTop sapUiSmallMarginBottom" wrapping="true"/>
                                </VBox>
                            </CustomListItem>
                        </List>
                    </VBox>
                </IconTabFilter>

                <!-- ALL STAYS TAB (Card Style) -->
                <IconTabFilter text="All Stays" icon="sap-icon://home">
                    <VBox class="sapUiMediumMargin">
                        <List items="{path: 'ResultsModel>/all_accommodation_options', templateShareable: false}" noDataText="No additional stays available.">
                            <CustomListItem>
                                <VBox class="card sapUiSmallMargin">
                                    <HBox alignItems="Start">
                                        <core:Icon src="sap-icon://home" class="sapUiTinyMarginEnd sapUiTinyMarginTop"/>
                                        <VBox>
                                            <Text text="🏨 {ResultsModel>name}" class="sapMTextBold sapUiSmallMarginEnd" wrapping="true"/>
                                            <Text text="🏷️ {ResultsModel>type}" wrapping="true"/>
                                            <Text text="📍 {ResultsModel>address}" visible="{= !!${ResultsModel>address} }" wrapping="true"/>
                                            <Text text="💰 ₹{ResultsModel>estimated_price_per_night} per night" wrapping="true"/>
                                        </VBox>
                                    </HBox>
                                    <Text text="✨ {ResultsModel>reason}" class="card-reason sapUiSmallMarginTop sapUiSmallMarginBottom" wrapping="true"/>
                                </VBox>
                            </CustomListItem>
                        </List>
                    </VBox>
                </IconTabFilter>

                <!-- FOODS & RESTAURANTS COMBINED TAB (Card Style) -->
                <IconTabFilter text="Foods &amp; Restaurants" icon="sap-icon://meal">
                    <VBox class="sapUiMediumMargin">
                        <Title text="🍽️ Must-Try Foods" level="H3" class="sapUiSmallMarginBottom"/>
                        <List items="{path: 'ResultsModel>/must_try_foods', templateShareable: false}" noDataText="No must-try foods listed.">
                            <CustomListItem>
                                <VBox class="card sapUiSmallMargin">
                                    <HBox alignItems="Start">
                                        <VBox>
                                            <core:HTML content="&lt;div class='food-photo-container'&gt;&lt;img src='{ResultsModel>photo/url}' alt='{ResultsModel>name}' style='width:120px;height:80px;object-fit:cover;border-radius:8px;' /&gt;&lt;/div&gt;" visible="{= !!${ResultsModel>photo/url} }" />
                                            <core:Icon src="sap-icon://meal" class="sapUiTinyMarginTop" visible="{= !${ResultsModel>photo/url} }"/>
                                        </VBox>
                                        <VBox class="sapUiSmallMarginStart">
                                            <Text text="🥘 {ResultsModel>name}" class="sapMTextBold" wrapping="true"/>
                                            <Text text="{ResultsModel>description}" wrapping="true"/>
                                            <Text text="📍 Where: {ResultsModel>where}" visible="{= !!${ResultsModel>where} }" wrapping="true"/>
                                            <Text text="💰 Cost: ₹{ResultsModel>cost}" visible="{= !!${ResultsModel>cost} }" wrapping="true"/>
                                        </VBox>
                                    </HBox>
                                </VBox >
                            </CustomListItem>
                        </List>
                        <Title text="🍴 Recommended Restaurants" level="H3" class="sapUiMediumMarginTop sapUiSmallMarginBottom"/>
                        <List items="{path: 'ResultsModel>/recommended_restaurants', templateShareable: false}" noDataText="No restaurants listed.">
                            <CustomListItem>
                                <VBox class="card sapUiSmallMargin">
                                    <HBox alignItems="Start">
                                        <core:Icon src="sap-icon://restaurant" class="sapUiTinyMarginEnd sapUiTinyMarginTop"/>
                                        <VBox>
                                            <Text text="🏪 {ResultsModel>name}" class="sapMTextBold" wrapping="true"/>
                                            <Text text="🏷️ {ResultsModel>type}, {ResultsModel>city}" wrapping="true"/>
                                            <Text text="📍 {ResultsModel>address}" visible="{= !!${ResultsModel>address} }" wrapping="true"/>
                                            <Text text="⭐ {ResultsModel>recommended_for}" visible="{= !!${ResultsModel>recommended_for} }" wrapping="true"/>
                                            <Text text="💰 Avg. Cost: ₹{ResultsModel>cost}" visible="{= !!${ResultsModel>cost} }" wrapping="true"/>
                                        </VBox>
                                    </HBox>
                                </VBox>
                            </CustomListItem>
                        </List>
                    </VBox>
                </IconTabFilter>

                <!-- TRANSPORTATION TAB -->
                <IconTabFilter text="Transportation" icon="sap-icon://bus-public-transport">
                    <VBox class="sapUiMediumMargin">
                        <List items="{path: 'ResultsModel>/transportation_options', templateShareable: false}" noDataText="No transportation options available.">
                            <CustomListItem>
                                <VBox class="card transportation-card sapUiSmallMargin">
                                    <HBox alignItems="Start">
                                        <core:Icon src="sap-icon://bus-public-transport" class="sapUiTinyMarginEnd sapUiTinyMarginTop"/>
                                        <VBox>
                                            <Text text="🚗 {ResultsModel>mode} - {ResultsModel>provider}" class="sapMTextBold" wrapping="true"/>
                                            <Text text="📍 {ResultsModel>route}" wrapping="true"/>
                                            <Text text="⏱️ Duration: {ResultsModel>duration}" wrapping="true"/>
                                            <Text text="💰 Cost: ₹{ResultsModel>estimated_cost}" wrapping="true"/>
                                            <Text text="👥 Best for: {ResultsModel>best_for}" wrapping="true"/>
                                            <Text text="{ResultsModel>description}" class="sapUiTinyMarginTop" wrapping="true"/>
                                            <Link text="🔗 Book Now" href="{ResultsModel>booking_url}" target="_blank" visible="{= !!${ResultsModel>booking_url} }" class="sapUiTinyMarginTop"/>
                                        </VBox>
                                    </HBox>
                                </VBox>
                            </CustomListItem>
                        </List>
                    </VBox>
                </IconTabFilter>

                <!-- MUST VISIT PLACES TAB -->
                <IconTabFilter text="Must Visit Places" icon="sap-icon://map-2">
                    <VBox class="sapUiMediumMargin">
                        <!-- Try must_visit_places first, then fall back to must_try_places -->
                        <List items="{path: 'ResultsModel>/must_visit_places', templateShareable: false}" noDataText="" visible="{= !!${ResultsModel>/must_visit_places} &amp;&amp; ${ResultsModel>/must_visit_places}.length > 0}">
                            <CustomListItem>
                                <VBox class="card places-card sapUiSmallMargin">
                                    <HBox alignItems="Start">
                                        <VBox>
                                            <core:HTML content="&lt;div class='photo-container'&gt;&lt;img src='{ResultsModel>photo/url}' alt='{ResultsModel>name}' style='width:120px;height:80px;object-fit:cover;' /&gt;&lt;/div&gt;" visible="{= !!${ResultsModel>photo/url} }" />
                                        </VBox>
                                        <VBox class="sapUiSmallMarginStart">
                                            <Text text="🏛️ {ResultsModel>name}" class="sapMTextBold" wrapping="true"/>
                                            <Text text="🏷️ {ResultsModel>type}" wrapping="true"/>
                                            <Text text="📍 {ResultsModel>location}" wrapping="true"/>
                                            <Text text="⏰ Best time: {ResultsModel>best_time_to_visit}" wrapping="true"/>
                                            <Text text="🎫 Entry fee: ₹{ResultsModel>entry_fee}" visible="{= ${ResultsModel>entry_fee} > 0 }" wrapping="true"/>
                                            <Text text="🕐 Time needed: {ResultsModel>estimated_time}" wrapping="true"/>
                                            <Text text="{ResultsModel>description}" class="sapUiTinyMarginTop" wrapping="true"/>
                                            <Text text="Tip: {ResultsModel>tips}" class="tip-text sapUiTinyMarginTop" visible="{= !!${ResultsModel>tips} }" wrapping="true"/>
                                        </VBox>
                                    </HBox>
                                </VBox>
                            </CustomListItem>
                        </List>
                        <!-- Fallback to must_try_places if must_visit_places doesn't exist -->
                        <List items="{path: 'ResultsModel>/must_try_places', templateShareable: false}" noDataText="No must-visit places available." visible="{= !${ResultsModel>/must_visit_places} || ${ResultsModel>/must_visit_places}.length === 0}">
                            <CustomListItem>
                                <VBox class="card places-card sapUiSmallMargin">
                                    <HBox alignItems="Start">
                                        <VBox>
                                            <core:HTML content="&lt;div class='photo-container'&gt;&lt;img src='{ResultsModel>photo/url}' alt='{ResultsModel>name}' style='width:120px;height:80px;object-fit:cover;' /&gt;&lt;/div&gt;" visible="{= !!${ResultsModel>photo/url} }" />
                                        </VBox>
                                        <VBox class="sapUiSmallMarginStart">
                                            <Text text="🏛️ {ResultsModel>name}" class="sapMTextBold" wrapping="true"/>
                                            <Text text="🏷️ {ResultsModel>type}" wrapping="true"/>
                                            <Text text="📍 {ResultsModel>location}" wrapping="true"/>
                                            <Text text="⏰ Best time: {ResultsModel>best_time_to_visit}" wrapping="true"/>
                                            <Text text="🎫 Entry fee: ₹{ResultsModel>entry_fee}" visible="{= ${ResultsModel>entry_fee} > 0 }" wrapping="true"/>
                                            <Text text="🕐 Time needed: {ResultsModel>estimated_time}" wrapping="true"/>
                                            <Text text="{ResultsModel>description}" class="sapUiTinyMarginTop" wrapping="true"/>
                                            <Text text="Tip: {ResultsModel>tips}" visible="{= !!${ResultsModel>tips} }" class="sapUiTinyMarginTop" wrapping="true"/>
                                        </VBox>
                                    </HBox>
                                </VBox>
                            </CustomListItem>
                        </List>
                    </VBox>
                </IconTabFilter>

                <!-- GLOBAL TIPS TAB -->
                <IconTabFilter text=" Travel Tips" icon="sap-icon://lightbulb" >
                    <VBox class="sapUiMediumMargin">
                        <List items="{path: 'ResultsModel>/overall_tips', templateShareable: false}" noDataText="No tips available.">
                            <CustomListItem>
                                <HBox alignItems="Center" class="sapUiSmallMargin">
                                    <Text text="{ResultsModel>}" wrapping="true" class="tip-text"/>
                                </HBox>
                            </CustomListItem>
                        </List>
                    </VBox>
                </IconTabFilter>

                <!-- EXPENSES TAB -->
                <IconTabFilter text="Expenses" icon="sap-icon://money-bills">
                    <VBox class="sapUiMediumMargin">
                        <Panel headerText="💰 Expense Breakdown" class="sapUiMediumMarginBottom expense-panel">
                            <VBox class="sapUiSmallMargin">
                                <!-- Accommodation -->
                                <HBox justifyContent="SpaceBetween" class="sapUiTinyMarginBottom">
                                    <Text text="🏨 Accommodation ({ResultsModel>/expense_breakdown/accommodation/nights} nights @ ₹{ResultsModel>/expense_breakdown/accommodation/per_night}/night)" class="sapMTextBold expense-label" wrapping="true"/>
                                    <Text text="₹{ResultsModel>/expense_breakdown/accommodation/total}" class="sapMTextBold expense-amount" wrapping="true"/>
                                </HBox>

                                <!-- Transportation -->
                                <HBox justifyContent="SpaceBetween" class="sapUiTinyMarginBottom">
                                    <VBox>
                                        <Text text="🚗 Transportation" class="sapMTextBold expense-label" wrapping="true"/>
                                        <Text text="{ResultsModel>/expense_breakdown/transportation/details}" class="sapUiTinyMarginTop expense-details" wrapping="true"/>
                                    </VBox>
                                    <Text text="₹{ResultsModel>/expense_breakdown/transportation/total}" class="sapMTextBold expense-amount" wrapping="true"/>
                                </HBox>

                                <!-- Food -->
                                <HBox justifyContent="SpaceBetween" class="sapUiTinyMarginBottom">
                                    <Text text="🍽️ Food (₹{ResultsModel>/expense_breakdown/food/per_day}/day)" class="sapMTextBold expense-label" wrapping="true"/>
                                    <Text text="₹{ResultsModel>/expense_breakdown/food/total}" class="sapMTextBold expense-amount" wrapping="true"/>
                                </HBox>

                                <!-- Activities -->
                                <HBox justifyContent="SpaceBetween" class="sapUiTinyMarginBottom">
                                    <VBox>
                                        <Text text="🎯 Activities &amp; Sightseeing" class="sapMTextBold expense-label" wrapping="true"/>
                                        <Text text="{ResultsModel>/expense_breakdown/activities/details}" class="sapUiTinyMarginTop expense-details" wrapping="true"/>
                                    </VBox>
                                    <Text text="₹{ResultsModel>/expense_breakdown/activities/total}" class="sapMTextBold expense-amount" wrapping="true"/>
                                </HBox>

                                <!-- Miscellaneous -->
                                <HBox justifyContent="SpaceBetween" class="sapUiTinyMarginBottom">
                                    <VBox>
                                        <Text text="🛍️ Miscellaneous" class="sapMTextBold expense-label" wrapping="true"/>
                                        <Text text="{ResultsModel>/expense_breakdown/miscellaneous/details}" class="sapUiTinyMarginTop expense-details" wrapping="true"/>
                                    </VBox>
                                    <Text text="₹{ResultsModel>/expense_breakdown/miscellaneous/total}" class="sapMTextBold expense-amount" wrapping="true"/>
                                </HBox>

                                <core:HTML content="&lt;hr style='margin: 16px 0; border: 1px solid #ddd;' /&gt;"/>

                                <!-- Total -->
                                <HBox justifyContent="SpaceBetween" class="sapUiSmallMarginTop">
                                    <Text text="💳 Total Estimated Cost" class="sapMTextBold sapUiLargeText expense-total-label" wrapping="true"/>
                                    <Text text="₹{ResultsModel>/expense_breakdown/total_estimated_cost}" class="sapMTextBold sapUiLargeText expense-total-amount" wrapping="true"/>
                                </HBox>
                            </VBox>
                        </Panel>
                    </VBox>
                </IconTabFilter>

            </items>
        </IconTabBar>
        <footer>
            <Toolbar>
                <ToolbarSpacer />
                <Button text="Export Plan" press="onExportPlan" type="Emphasized" icon="sap-icon://download"/>
                <Button text="Plan Another Trip" press="onRefreshPlan" icon="sap-icon://refresh"/>
            </Toolbar>
        </footer>
    </Page>
</mvc:View>
