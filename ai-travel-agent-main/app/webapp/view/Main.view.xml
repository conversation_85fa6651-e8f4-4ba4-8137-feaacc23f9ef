<mvc:View
    controllerName="travelapp.controller.Main"
    xmlns:mvc="sap.ui.core.mvc"
    xmlns="sap.m"
    xmlns:f="sap.f"
    xmlns:form="sap.ui.layout.form"
    xmlns:l="sap.ui.layout"
    xmlns:core="sap.ui.core"
    displayBlock="true"
    height="100%">
    <Page id="mainPage" showHeader="false">
        <content>
            <f:DynamicPage id="dynamicPageId" toggleHeaderOnTitleClick="false">
                <f:title>
                    <f:DynamicPageTitle>
                        <f:heading>
                            <VBox>
                                <Title text="{i18n>appTitle}" wrapping="true" />
                                <Text text="{i18n>appDescription}" />
                            </VBox>
                        </f:heading>
                    </f:DynamicPageTitle>
                </f:title>
                <f:content>
                    <VBox class="sapUiSmallMargin">
                        <form:SimpleForm
                            class="sapUiNoContentPadding"
                            editable="true"
                            layout="ResponsiveGridLayout"
                            labelSpanXL="4" labelSpanL="3" labelSpanM="4" labelSpanS="12"
                            adjustLabelSpan="false" emptySpanXL="0" emptySpanL="4"
                            emptySpanM="0" emptySpanS="0" columnsXL="2" columnsL="1" columnsM="1"
                            singleContainerFullSize="false">
                            <core:Title text="Trip Details" />
                            <Label text="{i18n>originLabel}" />
                            <Input id="originInput" value="Chennai" />
                            <Label text="{i18n>destinationLabel}" required="true" />
                            <Input id="destinationInput" placeholder="e.g., Manali, Goa, Kerala" />
                            <Label text="{i18n>datesLabel}" required="true" />
                            <DateRangeSelection id="dateRangeInput" displayFormat="MMM dd, yyyy" />
                            <Label text="{i18n>budgetLabel}" />
                            <Input id="budgetInput" type="Number" placeholder="e.g., 30000" description="₹" />
                            <Label text="{i18n>travelersLabel}" />
                            <Select id="travelersSelect" selectedKey="1">
                                <core:Item key="1" text="1 Traveler" />
                                <core:Item key="2" text="2 Travelers" />
                                <core:Item key="3" text="3 Travelers" />
                                <core:Item key="4" text="4 Travelers" />
                                <core:Item key="5" text="5 Travelers (Group)" />
                            </Select>
                            <Label text="Travel Style" />
                            <SegmentedButton id="styleSelector" selectedKey="Budget">
                                <items>
                                    <SegmentedButtonItem text="Budget" key="Budget" icon="sap-icon://money-bills"/>
                                    <SegmentedButtonItem text="Relaxation" key="Relaxation" icon="sap-icon://kpi-managing-my-area"/>
                                    <SegmentedButtonItem text="Adventure" key="Adventure" icon="sap-icon://activities"/>
                                    <SegmentedButtonItem text="Cultural" key="Cultural" icon="sap-icon://building"/>
                                </items>
                            </SegmentedButton>
                        </form:SimpleForm>
                        <Toolbar class="sapUiSmallMarginTop">
                            <ToolbarSpacer/>
                            <Button id="generateButton" text="{i18n>planButtonText}" press="onGeneratePlan" type="Emphasized" />
                        </Toolbar>
                    </VBox>
                </f:content>
            </f:DynamicPage>
        </content>
    </Page>
</mvc:View>