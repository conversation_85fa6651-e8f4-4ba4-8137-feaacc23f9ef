/* SAP-Style Travel App Custom Styles */
@import url('https://fonts.googleapis.com/css2?family=72:wght@300;400;500;600;700&display=swap');

/* Global Enhancements */
* {
    box-sizing: border-box;
}

/* Main App Background */
.sapMApp {
    background: linear-gradient(135deg, #0070f3 0%, #00d4ff 25%, #0070f3 50%, #764ba2 75%, #0070f3 100%);
    background-size: 400% 400%;
    animation: gradientFlow 20s ease infinite;
    min-height: 100vh;
    font-family: '72', 'SAP-icons', Arial, sans-serif;
}

@keyframes gradientFlow {
    0% { background-position: 0% 50%; }
    25% { background-position: 100% 50%; }
    50% { background-position: 50% 100%; }
    75% { background-position: 0% 50%; }
    100% { background-position: 0% 50%; }
}

/* Floating particles effect */
.sapMApp::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(255,255,255,0.1) 2px, transparent 2px),
        radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 2px, transparent 2px),
        radial-gradient(circle at 40% 40%, rgba(255,255,255,0.05) 1px, transparent 1px);
    background-size: 100px 100px, 150px 150px, 80px 80px;
    animation: float 30s linear infinite;
    pointer-events: none;
    z-index: 0;
}

@keyframes float {
    0% { transform: translateY(0px) rotate(0deg); }
    100% { transform: translateY(-100px) rotate(360deg); }
}

/* Dynamic Page Header */
.sapFDynamicPageTitle {
    background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.9) 100%);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    color: #0070f3;
    padding: 3rem 2rem;
    border-radius: 0 0 32px 32px;
    box-shadow:
        0 8px 32px rgba(0,112,243,0.15),
        0 0 0 1px rgba(255,255,255,0.2);
    position: relative;
    z-index: 10;
    margin-bottom: 2rem;
}

.sapFDynamicPageTitle::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #0070f3 0%, #00d4ff 100%);
    opacity: 0.1;
    border-radius: 0 0 32px 32px;
    z-index: -1;
}

.sapFDynamicPageTitle .sapMTitle {
    color: #0070f3;
    font-size: 3rem;
    font-weight: 300;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0,112,243,0.1);
    animation: titleGlow 3s ease-in-out infinite alternate;
}

@keyframes titleGlow {
    0% { text-shadow: 0 2px 4px rgba(0,112,243,0.1); }
    100% { text-shadow: 0 4px 20px rgba(0,112,243,0.3); }
}

.sapFDynamicPageTitle .sapMText {
    color: rgba(0,112,243,0.8);
    font-size: 1.2rem;
    font-weight: 400;
}

/* Form Container */
.sapUiFormSimpleForm {
    background: rgba(255,255,255,0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: 24px;
    padding: 3rem;
    box-shadow:
        0 20px 60px rgba(0,112,243,0.15),
        0 0 0 1px rgba(255,255,255,0.2),
        inset 0 1px 0 rgba(255,255,255,0.3);
    border: 1px solid rgba(0,112,243,0.1);
    margin: 2rem;
    position: relative;
    z-index: 5;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sapUiFormSimpleForm:hover {
    transform: translateY(-4px);
    box-shadow:
        0 30px 80px rgba(0,112,243,0.2),
        0 0 0 1px rgba(255,255,255,0.3),
        inset 0 1px 0 rgba(255,255,255,0.4);
}

/* Enhanced Input Fields */
.sapMInput, .sapMSelect, .sapMDateRangeSelection {
    border-radius: 12px !important;
    border: 2px solid rgba(0,112,243,0.1) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    background: rgba(255,255,255,0.8) !important;
}

.sapMInput:hover, .sapMSelect:hover, .sapMDateRangeSelection:hover {
    border-color: rgba(0,112,243,0.3) !important;
    box-shadow: 0 4px 20px rgba(0,112,243,0.1) !important;
    transform: translateY(-1px) !important;
}

.sapMInput:focus, .sapMSelect:focus, .sapMDateRangeSelection:focus {
    border-color: #0070f3 !important;
    box-shadow:
        0 0 0 3px rgba(0,112,243,0.1),
        0 8px 30px rgba(0,112,243,0.15) !important;
    transform: translateY(-2px) !important;
}

/* Card Styling */
.card {
    border: 1px solid rgba(0,112,243,0.1);
    border-radius: 24px;
    padding: 24px;
    background: rgba(255,255,255,0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    margin-bottom: 20px;
    box-shadow:
        0 8px 32px rgba(0,112,243,0.1),
        0 0 0 1px rgba(255,255,255,0.2),
        inset 0 1px 0 rgba(255,255,255,0.3);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(90deg, #0070f3, #00d4ff, #764ba2, #0070f3);
    background-size: 300% 100%;
    animation: cardGradient 8s ease infinite;
}

@keyframes cardGradient {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0,112,243,0.02) 0%, rgba(0,212,255,0.02) 100%);
    border-radius: 24px;
    pointer-events: none;
    z-index: 0;
}

.card > * {
    position: relative;
    z-index: 1;
}

.card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
        0 20px 60px rgba(0,112,243,0.2),
        0 0 0 1px rgba(255,255,255,0.3),
        inset 0 1px 0 rgba(255,255,255,0.4),
        0 0 40px rgba(0,212,255,0.1);
    border-color: rgba(0,112,243,0.3);
}

.card-reason {
    color: #34505f;
    font-size: 0.98rem;
    font-style: italic;
    line-height: 1.4;
}

/* SAP-Style Panels */
.sapMPanel {
    border-radius: 16px;
    overflow: visible;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    margin-bottom: 1.5rem;
    border: 1px solid rgba(0,112,243,0.1);
    min-height: auto !important;
    height: auto !important;
}

/* Panel Content */
.sapMPanel .sapMPanelContent {
    overflow: visible !important;
    max-height: none !important;
    height: auto !important;
    padding: 1rem !important;
}

/* Panel Header */
.sapMPanel .sapMPanelHdr {
    min-height: 48px !important;
    height: auto !important;
    background: linear-gradient(135deg, #0070f3 0%, #00d4ff 100%) !important;
    border-radius: 16px 16px 0 0 !important;
    padding: 1rem 1.5rem !important;
}

.sapMPanel .sapMPanelHdr {
    background: linear-gradient(135deg, #0070f3 0%, #0056b3 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 0;
    border: none;
}

.sapMPanel .sapMPanelHdrText {
    color: white !important;
    font-weight: 600 !important;
    font-size: 1.2rem !important;
}

/* Expense Panel Header Fix */
.expense-panel .sapMPanelHdr {
    background: linear-gradient(135deg, #0070f3 0%, #00d4ff 100%) !important;
    border-radius: 16px 16px 0 0 !important;
    padding: 1rem 1.5rem !important;
}

.expense-panel .sapMPanelHdrText {
    color: white !important;
    font-weight: 700 !important;
    font-size: 1.3rem !important;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2) !important;
}

/* Force panel expansion and content visibility */
.sapMPanel.sapMPanelExpanded .sapMPanelContent {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    max-height: none !important;
    height: auto !important;
    overflow: visible !important;
}

/* Ensure expanded panels show content */
.sapMPanel[aria-expanded="true"] .sapMPanelContent {
    display: block !important;
    max-height: none !important;
    height: auto !important;
}

.sapMPanel .sapMPanelContent {
    background: white;
    padding: 1.5rem;
    border-radius: 0;
}

/* Activity Item Styling */
.activity-item {
    background: linear-gradient(135deg, #f8f9ff 0%, #fff 100%);
    border-radius: 12px;
    padding: 1.5rem;
    margin: 1rem 0;
    border-left: 4px solid #0070f3;
    box-shadow: 0 2px 12px rgba(0,112,243,0.1);
    transition: all 0.3s ease;
}

.activity-item:hover {
    transform: translateX(4px);
    box-shadow: 0 4px 20px rgba(0,112,243,0.15);
}

/* SAP-Style Icon Tab Bar */
.sapMITH {
    background: rgba(255,255,255,0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: 24px 24px 0 0;
    box-shadow:
        0 -8px 32px rgba(0,112,243,0.1),
        0 0 0 1px rgba(255,255,255,0.2);
    padding: 0.5rem 1.5rem;
    position: relative;
    z-index: 10;
}

.sapMITH::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #0070f3, #00d4ff, #764ba2, #0070f3);
    background-size: 300% 100%;
    animation: tabGradient 10s ease infinite;
    border-radius: 24px 24px 0 0;
}

@keyframes tabGradient {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.sapMITH .sapMITBItem {
    border-radius: 16px 16px 0 0;
    margin: 0 0.5rem;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.sapMITH .sapMITBItem::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0,112,243,0.05) 0%, rgba(0,212,255,0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.sapMITH .sapMITBItem:hover::before {
    opacity: 1;
}

.sapMITH .sapMITBItem:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,112,243,0.15);
}

.sapMITH .sapMITBItem.sapMITBSelected {
    background: linear-gradient(135deg, #0070f3 0%, #00d4ff 50%, #0070f3 100%);
    background-size: 200% 200%;
    animation: selectedTabGradient 4s ease infinite;
    color: white;
    transform: translateY(-3px);
    box-shadow:
        0 8px 30px rgba(0,112,243,0.3),
        0 0 20px rgba(0,212,255,0.2);
}

@keyframes selectedTabGradient {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.sapMITH .sapMITBItem.sapMITBSelected .sapMITBText {
    color: white;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

/* Enhanced Button Styling */
.sapMBtn {
    border-radius: 16px !important;
    font-weight: 600 !important;
    text-transform: none !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    position: relative !important;
    overflow: hidden !important;
}

.sapMBtn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
    z-index: 1;
}

.sapMBtn:hover::before {
    left: 100%;
}

.sapMBtn.sapMBtnEmphasized {
    background: linear-gradient(135deg, #0070f3 0%, #00d4ff 50%, #0070f3 100%) !important;
    background-size: 200% 200% !important;
    border: none !important;
    border-radius: 20px !important;
    box-shadow:
        0 8px 30px rgba(0,112,243,0.3),
        0 0 0 1px rgba(255,255,255,0.1) !important;
    padding: 1rem 3rem !important;
    font-weight: 700 !important;
    font-size: 1.1rem !important;
    color: white !important;
    animation: buttonGradient 4s ease infinite !important;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1) !important;
}

@keyframes buttonGradient {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.sapMBtn.sapMBtnEmphasized:hover {
    transform: translateY(-4px) scale(1.05) !important;
    box-shadow:
        0 20px 50px rgba(0,112,243,0.4),
        0 0 0 3px rgba(0,212,255,0.3),
        0 0 30px rgba(0,212,255,0.2) !important;
    animation-duration: 1.5s !important;
}

.sapMBtn.sapMBtnEmphasized:active {
    transform: translateY(-2px) scale(1.02) !important;
    animation-duration: 0.5s !important;
}

/* List Item Styling */
.sapMListItem {
    border-radius: 8px;
    margin: 4px 0;
}

/* Title Styling */
.sapMTitle {
    color: #2c3e50;
    font-weight: 600;
}

/* Transportation Card Styling */
.transportation-card {
    background: linear-gradient(135deg, #e3f2fd 0%, #f0f8ff 100%);
    border-left: 6px solid #2196f3;
    position: relative;
}

.transportation-card::before {
    background: linear-gradient(90deg, #2196f3, #00bcd4);
}

/* Places Card Styling */
.places-card {
    background: linear-gradient(135deg, #fff3e0 0%, #fafafa 100%);
    border-left: 6px solid #ff9800;
    position: relative;
}

.places-card::before {
    background: linear-gradient(90deg, #ff9800, #ffc107);
}

/* Photo Container */
.photo-container {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    transition: all 0.3s ease;
    position: relative;
}

.photo-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 0%, rgba(0,112,243,0.1) 100%);
    pointer-events: none;
}

.photo-container:hover {
    transform: scale(1.02);
    box-shadow: 0 8px 30px rgba(0,0,0,0.2);
}

/* Food Photo Container */
.food-photo-container {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    transition: all 0.3s ease;
    position: relative;
}

.food-photo-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 0%, rgba(255,152,0,0.1) 100%);
    pointer-events: none;
}

.food-photo-container:hover {
    transform: scale(1.02);
    box-shadow: 0 8px 30px rgba(0,0,0,0.2);
}

.food-photo-container img {
    transition: transform 0.3s ease;
}

.food-photo-container img:hover {
    transform: scale(1.05);
}

/* Enhanced Link Styling */
.sapMLink {
    color: #0070f3;
    font-weight: 600;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    background: rgba(0,112,243,0.1);
    transition: all 0.3s ease;
    display: inline-block;
}

.sapMLink:hover {
    color: white;
    background: #0070f3;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,112,243,0.3);
}

/* Text Wrapping for Tips */
.tip-text {
    white-space: normal !important;
    word-wrap: break-word !important;
    overflow: visible !important;
    text-overflow: clip !important;
    line-height: 1.6;
    color: #333;
    max-height: none !important;
    height: auto !important;
}

/* Ensure all text content is fully visible */
.sapMText, .sapMTextBold {
    overflow: visible !important;
    text-overflow: clip !important;
    white-space: normal !important;
    max-height: none !important;
    height: auto !important;
}

/* Fix container heights to prevent text truncation */
.sapUiSmallMargin, .sapUiTinyMargin, .sapUiMediumMargin {
    overflow: visible !important;
    max-height: none !important;
    height: auto !important;
}

/* Fix all VBox and HBox containers */
.sapMVBox, .sapMHBox {
    overflow: visible !important;
    max-height: none !important;
    height: auto !important;
    min-height: auto !important;
}

/* Fix List containers */
.sapMList, .sapMListUl {
    overflow: visible !important;
    max-height: none !important;
    height: auto !important;
}

/* Fix CustomListItem */
.sapMCustomListItem {
    overflow: visible !important;
    max-height: none !important;
    height: auto !important;
    min-height: auto !important;
}

/* Fix IconTabBar content */
.sapMITBContent, .sapMITBContentClosed {
    overflow: visible !important;
    max-height: none !important;
    height: auto !important;
}

/* Ensure page content is fully visible */
.sapMPage .sapMPageContent {
    overflow: visible !important;
    max-height: none !important;
    height: auto !important;
}

/* Fix IconTabFilter content areas */
.sapMITBFilter {
    overflow: visible !important;
    max-height: none !important;
    height: auto !important;
}

/* Ensure all content containers are visible */
.sapUiResponsiveContentPadding {
    overflow: visible !important;
    max-height: none !important;
    height: auto !important;
}

/* Fix any remaining height restrictions */
* {
    box-sizing: border-box;
}

.sapMPanel, .sapMPanel * {
    max-height: none !important;
    overflow: visible !important;
}

/* Enhanced Text Styling */
.sapMText {
    line-height: 1.6 !important;
    color: #2c3e50 !important;
}

.sapMTextBold {
    font-weight: 600 !important;
    color: #0070f3 !important;
}

/* Expense Tab Text Styling */
.expense-label {
    color: #2c3e50 !important;
    font-weight: 600 !important;
    font-size: 1rem !important;
}

.expense-details {
    color: #5a6c7d !important;
    font-size: 0.9rem !important;
    line-height: 1.4 !important;
}

.expense-amount {
    color: #0070f3 !important;
    font-weight: 700 !important;
    font-size: 1.1rem !important;
}

.expense-total-label {
    color: #2c3e50 !important;
    font-weight: 700 !important;
    font-size: 1.3rem !important;
}

.expense-total-amount {
    color: #28a745 !important;
    font-weight: 800 !important;
    font-size: 1.4rem !important;
    text-shadow: 0 1px 2px rgba(40, 167, 69, 0.2) !important;
}

/* Loading Animation */
.sapMBusyIndicator {
    background: rgba(255,255,255,0.95) !important;
    backdrop-filter: blur(20px) !important;
    border-radius: 20px !important;
    box-shadow: 0 20px 60px rgba(0,112,243,0.2) !important;
}

/* Smooth Page Transitions */
.sapMPage {
    animation: pageSlideIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes pageSlideIn {
    0% {
        opacity: 0;
        transform: translateY(30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(0,112,243,0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #0070f3, #00d4ff);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #0056b3, #00b8d4);
}

/* Premium Visual Effects */
.sapMApp::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 25% 25%, rgba(0,112,243,0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(0,212,255,0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: 1;
}

/* Enhanced Form Labels */
.sapMLabel {
    font-weight: 600 !important;
    color: #0070f3 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    font-size: 0.9rem !important;
}

/* Premium List Styling */
.sapMList {
    background: transparent !important;
}

.sapMListUl {
    background: transparent !important;
}

.sapMCustomListItem {
    background: transparent !important;
    border: none !important;
}

/* Enhanced Icons */
.sapUiIcon {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.sapUiIcon:hover {
    transform: scale(1.1) !important;
    filter: drop-shadow(0 4px 8px rgba(0,112,243,0.3)) !important;
}

/* Responsive Design Enhancements */
@media (max-width: 768px) {
    .sapFDynamicPageTitle {
        padding: 2rem 1rem;
    }

    .sapFDynamicPageTitle .sapMTitle {
        font-size: 2rem;
    }

    .sapUiFormSimpleForm {
        margin: 1rem;
        padding: 2rem;
    }

    .card {
        margin-bottom: 16px;
        padding: 20px;
    }

    .sapMBtn.sapMBtnEmphasized {
        padding: 1rem 2rem !important;
        font-size: 1rem !important;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .sapMApp {
        background: linear-gradient(135deg, #1a1a2e 0%, #16213e 25%, #0f3460 50%, #16213e 75%, #1a1a2e 100%);
    }

    .card {
        background: rgba(255,255,255,0.1) !important;
        border-color: rgba(255,255,255,0.2) !important;
    }

    .sapMText {
        color: #e0e0e0 !important;
    }
}

/* Results Page Styling */
.resultsPage {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.resultsPage .sapMPageHeader {
    background: linear-gradient(135deg, #0070f3 0%, #0056b3 100%);
    color: white;
    box-shadow: 0 4px 20px rgba(0,112,243,0.2);
}

.resultsPage .sapMPageHeader .sapMTitle {
    color: white;
    font-weight: 300;
}

/* Tab Content Styling */
.sapMITBContentContainer {
    background: white;
    border-radius: 0 0 16px 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    margin: 0 1rem 1rem 1rem;
}

/* List Styling */
.sapMList {
    background: transparent;
    border: none;
}

.sapMListItem {
    border-radius: 12px;
    margin: 0.5rem 0;
    background: transparent;
    border: none;
}

/* Enhanced Typography */
.sapMTextBold {
    font-weight: 600;
    color: #2c3e50;
}

.card-reason {
    color: #5a6c7d;
    font-size: 0.95rem;
    font-style: italic;
    line-height: 1.5;
    margin-top: 0.5rem;
}

/* Footer Styling */
.sapMPageFooter {
    background: white;
    border-top: 1px solid rgba(0,112,243,0.1);
    box-shadow: 0 -4px 20px rgba(0,0,0,0.05);
}
