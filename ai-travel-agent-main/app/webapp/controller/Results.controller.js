sap.ui.define([
    "sap/ui/core/mvc/Controller",
    "sap/ui/core/routing/History",
    "sap/m/MessageBox"
], function (Controller, History, MessageBox) {
    "use strict";
    return Controller.extend("travelapp.controller.Results", {
        onInit: function () {
            this.getOwnerComponent().getRouter()
                .getRoute("results").attachPatternMatched(this._onPatternMatched, this);
        },
        _onPatternMatched: function () {
            const oResultsModel = this.getOwnerComponent().getModel("ResultsModel");
            const oData = oResultsModel.getData();

            // Ensure all required data structures exist with fallbacks
            this._ensureDataStructure(oResultsModel, oData);

            // Force refresh the model
            oResultsModel.refresh(true);

            // Debug: Log the current data structure
            console.log("Current model data:", oResultsModel.getData());
        },

        _ensureDataStructure: function(oResultsModel, oData) {
            // Ensure itinerary exists
            if (oData.days && !oData.itinerary) oResultsModel.setProperty("/itinerary", oData.days);
            if (oData.daily_itinerary && !oData.itinerary) oResultsModel.setProperty("/itinerary", oData.daily_itinerary);
            if (!oData.itinerary) oResultsModel.setProperty("/itinerary", []);

            // Handle backward compatibility: convert must_try_places to must_visit_places
            if (oData.must_try_places && !oData.must_visit_places) {
                console.log("Converting must_try_places to must_visit_places:", oData.must_try_places);
                oResultsModel.setProperty("/must_visit_places", oData.must_try_places);
            }

            // Ensure all required arrays exist
            if (!oData.recommended_accommodation_options) oResultsModel.setProperty("/recommended_accommodation_options", []);
            if (!oData.all_accommodation_options) oResultsModel.setProperty("/all_accommodation_options", []);
            if (!oData.must_try_foods) oResultsModel.setProperty("/must_try_foods", []);
            if (!oData.recommended_restaurants) oResultsModel.setProperty("/recommended_restaurants", []);
            if (!oData.must_visit_places) oResultsModel.setProperty("/must_visit_places", []);
            if (!oData.transportation_options) oResultsModel.setProperty("/transportation_options", []);
            if (!oData.overall_tips) oResultsModel.setProperty("/overall_tips", []);

            // Ensure expense_breakdown exists
            if (!oData.expense_breakdown) {
                oResultsModel.setProperty("/expense_breakdown", {
                    accommodation: { total: 0, per_night: 0, nights: 0 },
                    transportation: { total: 0, details: "No data" },
                    food: { total: 0, per_day: 0 },
                    activities: { total: 0, details: "No data" },
                    miscellaneous: { total: 0, details: "No data" },
                    total_estimated_cost: 0
                });
            }

            // Ensure trip_summary exists
            if (!oData.trip_summary) {
                oResultsModel.setProperty("/trip_summary", {
                    title: "Travel Plan",
                    destination: "Unknown",
                    duration: "0 Days",
                    total_budget: 0
                });
            }
        },
        onNavBack: function () {
            this.getRouter().navTo("main");
        },
        onExportPlan: function() {
            const oResultsModel = this.getOwnerComponent().getModel("ResultsModel");
            const oData = oResultsModel.getData();
            if (oData) {
                const sJsonString = JSON.stringify(oData, null, 2);
                const oBlob = new Blob([sJsonString], { type: 'application/json' });
                const sUrl = URL.createObjectURL(oBlob);
                const oLink = document.createElement('a');
                oLink.href = sUrl;
                oLink.download = `travel-plan-${oData.trip_summary?.destination || 'export'}.json`;
                document.body.appendChild(oLink);
                oLink.click();
                document.body.removeChild(oLink);
                URL.revokeObjectURL(sUrl);
            }
        },
        onRefreshPlan: function() {
            this.getRouter().navTo("main");
        },
        getRouter: function () {
            return this.getOwnerComponent().getRouter();
        }
    });
});
