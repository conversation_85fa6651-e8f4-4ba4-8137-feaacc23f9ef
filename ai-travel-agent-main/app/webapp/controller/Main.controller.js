sap.ui.define([
    "sap/ui/core/mvc/Controller",
    "sap/m/MessageBox"
], function (Controller, MessageBox) {
    "use strict";

    return Controller.extend("travelapp.controller.Main", {

        onInit: function () {},

        onAfterRendering: function() { this._initializeForm(); },

        _initializeForm: function() {
            this.byId("budgetInput").setValue("30000");
            this.byId("travelersSelect").setSelectedKey("1");
            const oDateRange = this.byId("dateRangeInput");
            if (oDateRange) {
                const dToday = new Date();
                const dStartDate = new Date();
                dStartDate.setDate(dToday.getDate() + 6); // Start from Aug 8 (6 days from Aug 2)
                const dEndDate = new Date();
                dEndDate.setDate(dToday.getDate() + 11); // End on Aug 13 (5 days trip)
                oDateRange.setDateValue(dStartDate);
                oDateRange.setSecondDateValue(dEndDate);
                oDateRange.setMinDate(dToday);
            }
        },

        onGeneratePlan: function (oEvent) {
            const oButton = oEvent.getSource();
            const sOrigin = this.byId("originInput").getValue().trim();
            const sDestination = this.byId("destinationInput").getValue().trim();
            const oDateRange = this.byId("dateRangeInput");
            const sBudget = this.byId("budgetInput").getValue();
            const sTravelers = this.byId("travelersSelect").getSelectedKey();
            const sTravelStyle = this.byId("styleSelector").getSelectedKey();
            const dStartDate = oDateRange.getDateValue();
            const dEndDate = oDateRange.getSecondDateValue();

            if (!sDestination || !dStartDate || !dEndDate) {
                MessageBox.error("Please provide a Destination and a complete Date Range (Start and End Date).");
                return;
            }

            oButton.setBusy(true);
            const oModel = this.getView().getModel();
            const oAction = oModel.bindContext("/generatePlan(...)");
            oAction.setParameter("origin", sOrigin || "Not specified");
            oAction.setParameter("destination", sDestination);
            oAction.setParameter("startDate", dStartDate.toISOString().split('T')[0]);
            oAction.setParameter("endDate", dEndDate.toISOString().split('T')[0]);
            oAction.setParameter("budget", parseInt(sBudget));
            oAction.setParameter("travelers", parseInt(sTravelers));
            oAction.setParameter("travelStyle", sTravelStyle);

            oAction.execute().then(function () {
                oButton.setBusy(false);
                const oContext = oAction.getBoundContext();
                const oResult = oContext.getObject();

                if (!oResult || !oResult.planJson) {
                    MessageBox.error("The server's response did not contain the expected travel plan data.");
                    return;
                }

                try {
                    const travelPlan = JSON.parse(oResult.planJson);
                    const oResultsModel = this.getOwnerComponent().getModel("ResultsModel");
                    oResultsModel.setData(travelPlan);
                    this.getRouter().navTo("results");
                } catch (e) {
                    MessageBox.error("Failed to parse the travel plan from the server. The AI may have returned an invalid format.");
                }
            }.bind(this)).catch(function (oError) {
                oButton.setBusy(false);
                MessageBox.error("Failed to generate travel plan: " + oError.message);
            });
        },

        getRouter: function () { return this.getOwnerComponent().getRouter(); }
    });
});
