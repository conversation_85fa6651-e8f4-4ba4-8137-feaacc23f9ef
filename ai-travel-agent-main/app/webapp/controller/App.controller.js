sap.ui.define([
    "sap/ui/core/mvc/Controller",
    "sap/m/MessageBox"
], function (Controller, MessageBox) {
    "use strict";

    return Controller.extend("travelapp.controller.App", {

        onInit: function () {
            // Initialize the root application controller
            console.log("App controller initialized");
            this._initializeApp();
        },

        _initializeApp: function() {
            try {
                // Set up any global app configurations
                this._setupGlobalHandlers();
                console.log("App initialization completed");
            } catch (error) {
                console.error("App initialization failed:", error);
                MessageBox.error("Failed to initialize application");
            }
        },

        _setupGlobalHandlers: function() {
            // Set up global error handling
            window.addEventListener('error', function(event) {
                console.error('Global error:', event.error);
            });

            // Set up unhandled promise rejection handling
            window.addEventListener('unhandledrejection', function(event) {
                console.error('Unhandled promise rejection:', event.reason);
            });
        },

        onAfterRendering: function() {
            // Called after the view is rendered
            console.log("App view rendered");
        },

        onBeforeRendering: function() {
            // Called before the view is rendered
            console.log("App view rendering");
        },

        onExit: function() {
            // Called when the view is destroyed
            console.log("App controller destroyed");
        }
    });
}); 