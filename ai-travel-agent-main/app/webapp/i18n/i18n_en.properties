# HORIZON Travel Discovery Engine - English
# This file extends the base i18n.properties for English-specific content

# HORIZON Specific
horizonTitle=HORIZON - AI Travel Discovery Engine
horizonSubtitle=Discover the world with AI-powered visual travel planning
horizonDescription=🤖 AI Intelligence • 📸 Visual Discovery • 🌤️ Real-time Weather • 💰 Budget Optimization

# Global Travel
globalTravel=Global Travel Discovery
anyDestination=Any destination worldwide
internationalTravel=International Travel
domesticTravel=Domestic Travel

# Enhanced Features
visualDiscovery=Visual Discovery
realTimeWeather=Real-time Weather
budgetOptimization=Budget Optimization
aiIntelligence=AI Intelligence
multiApiOrchestration=Multi-API Orchestration

# Currency Support
localCurrency=Local Currency
usdConversion=USD Conversion
currencyNote=Prices shown in local currency with USD equivalent

# Enhanced Error Messages
quotaExceeded=API quota exceeded. Please wait a few minutes and try again.
invalidApiKey=Invalid API key. Please check your configuration.
networkTimeout=Request timeout. Please check your connection and try again.
serviceError=Service temporarily unavailable. Please try again later.

# Success Messages
horizonComplete=HORIZON travel discovery completed successfully!
planGenerated=Your visual travel plan has been generated!
imagesLoaded=Visual content loaded successfully
weatherUpdated=Weather information updated

# Loading Messages
horizonActivated=HORIZON Discovery Engine Activated
generatingVisualPlan=Generating visual travel plan...
fetchingImages=Fetching visual content...
processingWeather=Processing weather data...
orchestratingApis=Orchestrating multiple APIs...

# Model Information
aiModel=AI Model
modelDetection=Detecting available models...
usingModel=Using model: {0}
modelOptimized=Model optimized for best performance
