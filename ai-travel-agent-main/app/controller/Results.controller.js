sap.ui.define([
    "sap/ui/core/mvc/Controller",
    "sap/ui/model/json/JSONModel",
    "sap/m/MessageToast"
], function (Controller, JSONModel, MessageToast) {
    "use strict";

    return Controller.extend("ai.travel.planner.controller.Results", {

        onInit: function () {
            this.getRouter().getRoute("RouteResults").attachPatternMatched(this._onRouteMatched, this);
        },

        _onRouteMatched: function (oEvent) {
            var oArgs = oEvent.getParameter("arguments");
            var sQuery = oArgs["?query"];
            
            if (sQuery && sQuery.plan) {
                try {
                    var oPlanData = JSON.parse(decodeURIComponent(sQuery.plan));
                    this._loadTravelPlan(oPlanData);
                } catch (e) {
                    MessageToast.show("Error loading travel plan data");
                    this.onNavBack();
                }
            } else {
                MessageToast.show("No travel plan data found");
                this.onNavBack();
            }
        },

        _loadTravelPlan: function (oPlanData) {
            // Show loading
            var oModel = new JSONModel({
                tripTitle: `A ${oPlanData.travelStyle} Journey to ${oPlanData.destination}`,
                destination: oPlanData.destination,
                duration: this._calculateDuration(oPlanData.startDate, oPlanData.endDate),
                budget: oPlanData.budget,
                travelers: oPlanData.travelers + " Traveler" + (oPlanData.travelers > 1 ? "s" : ""),
                itinerary: [],
                mustVisitPlaces: [],
                localFoods: [],
                accommodations: [],
                budgetBreakdown: [],
                totalBudget: 0
            });
            
            this.getView().setModel(oModel);
            
            // Fetch actual travel plan from backend
            this._fetchTravelPlanFromBackend(oPlanData);
        },

        _fetchTravelPlanFromBackend: function (oPlanData) {
            // Make API call to get the generated travel plan
            jQuery.ajax({
                url: "/service/plan/generate",
                type: "POST",
                contentType: "application/json",
                data: JSON.stringify(oPlanData),
                success: function (data) {
                    if (data && data.itinerary) {
                        this._updateModelWithTravelPlan(data);
                    } else {
                        MessageToast.show("No travel plan data received");
                    }
                }.bind(this),
                error: function (xhr, status, error) {
                    MessageToast.show("Error loading travel plan details");
                    console.error("Error:", error);
                    // Load sample data for demo
                    this._loadSampleData();
                }
            });
        },

        _updateModelWithTravelPlan: function (oPlan) {
            var oModel = this.getView().getModel();
            var oData = oModel.getData();
            
            // Update with real data
            oData.itinerary = this._formatItinerary(oPlan.itinerary);
            oData.mustVisitPlaces = oPlan.must_visit_places || [];
            oData.localFoods = oPlan.local_foods || [];
            oData.accommodations = oPlan.accommodations || [];
            oData.budgetBreakdown = this._formatBudgetBreakdown(oPlan.budget_breakdown);
            oData.totalBudget = oPlan.total_estimated_cost || oData.budget;
            
            oModel.setData(oData);
        },

        _formatItinerary: function (aItinerary) {
            return aItinerary.map(function (oDay) {
                return {
                    day: oDay.day,
                    date: oDay.date,
                    activities: oDay.activities || [],
                    weather: oDay.weather
                };
            });
        },

        _formatBudgetBreakdown: function (oBudget) {
            if (!oBudget) return [];
            
            return Object.keys(oBudget).map(function (sKey) {
                return {
                    category: sKey.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
                    amount: oBudget[sKey]
                };
            });
        },

        _loadSampleData: function () {
            // Sample data for demo purposes
            var oModel = this.getView().getModel();
            var oData = oModel.getData();
            
            oData.itinerary = [
                {
                    day: "Day 1: Arrival and Old Manali Charm",
                    date: "2025-08-13",
                    activities: [
                        { time: "Morning", activity: "Arrival & Check-in", description: "Arrive in Manali, check into accommodation" },
                        { time: "Afternoon", activity: "Old Manali Exploration", description: "Visit Old Manali market and cafes" },
                        { time: "Evening", activity: "Manu Temple Visit", description: "Visit the ancient Manu Temple" }
                    ],
                    weather: { description: "Partly Cloudy", temp: 22 }
                }
            ];
            
            oData.mustVisitPlaces = [
                { name: "Rohtang Pass", description: "Scenic mountain pass with snow activities", category: "Adventure" },
                { name: "Solang Valley", description: "Adventure sports and beautiful valley views", category: "Adventure" }
            ];
            
            oData.localFoods = [
                { name: "Himachali Dham", description: "Traditional feast with local delicacies", price_range: "200-400" },
                { name: "Siddu", description: "Steamed bread stuffed with poppy seeds", price_range: "50-100" }
            ];
            
            oModel.setData(oData);
        },

        _calculateDuration: function (sStartDate, sEndDate) {
            var oStart = new Date(sStartDate);
            var oEnd = new Date(sEndDate);
            var iDays = Math.ceil((oEnd - oStart) / (1000 * 60 * 60 * 24)) + 1;
            return iDays + " Day" + (iDays > 1 ? "s" : "");
        },

        onNavBack: function () {
            this.getRouter().navTo("RouteMain");
        },

        onExportPlan: function () {
            MessageToast.show("Export functionality coming soon!");
        },

        onPlanAnother: function () {
            this.getRouter().navTo("RouteMain");
        },

        getRouter: function () {
            return this.getOwnerComponent().getRouter();
        }
    });
});
