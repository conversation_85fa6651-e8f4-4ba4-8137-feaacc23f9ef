sap.ui.define([
    "sap/ui/core/mvc/Controller",
    "sap/ui/model/json/JSONModel",
    "sap/m/MessageToast",
    "sap/m/BusyDialog"
], function (Controller, JSONModel, MessageToast, BusyDialog) {
    "use strict";

    return Controller.extend("ai.travel.planner.controller.Main", {

        onInit: function () {
            // Initialize form model
            var oFormModel = new JSONModel({
                origin: "",
                destination: "",
                startDate: null,
                endDate: null,
                budget: "",
                travelers: "",
                travelStyleIndex: -1
            });
            this.getView().setModel(oFormModel);
            
            // Set minimum date to today
            var today = new Date();
            this.byId("startDatePicker").setMinDate(today);
            this.byId("endDatePicker").setMinDate(today);
        },

        onGeneratePlan: function () {
            var oModel = this.getView().getModel();
            var oData = oModel.getData();
            
            // Validate form
            if (!this._validateForm(oData)) {
                MessageToast.show("Please fill in all required fields");
                return;
            }
            
            // Convert travel style index to text
            var aTravelStyles = ["Budget", "Relaxation", "Adventure", "Cultural"];
            var sTravelStyle = aTravelStyles[oData.travelStyleIndex];
            
            // Prepare request data
            var oRequestData = {
                origin: oData.origin,
                destination: oData.destination,
                startDate: this._formatDate(oData.startDate),
                endDate: this._formatDate(oData.endDate),
                budget: parseInt(oData.budget),
                travelers: parseInt(oData.travelers),
                travelStyle: sTravelStyle
            };
            
            // Show loading dialog
            var oBusyDialog = new BusyDialog({
                title: "Generating Your Perfect Trip...",
                text: "Our AI is crafting a personalized itinerary just for you!"
            });
            oBusyDialog.open();
            
            // Make API call
            jQuery.ajax({
                url: "/service/plan/generate",
                type: "POST",
                contentType: "application/json",
                data: JSON.stringify(oRequestData),
                success: function (data) {
                    oBusyDialog.close();
                    // Navigate to results page
                    this.getRouter().navTo("RouteResults", {
                        query: {
                            plan: encodeURIComponent(JSON.stringify(oRequestData))
                        }
                    });
                }.bind(this),
                error: function (xhr, status, error) {
                    oBusyDialog.close();
                    MessageToast.show("Sorry, there was an error generating your travel plan. Please try again.");
                    console.error("Error:", error);
                }
            });
        },

        _validateForm: function (oData) {
            return oData.origin && 
                   oData.destination && 
                   oData.startDate && 
                   oData.endDate && 
                   oData.budget && 
                   oData.travelers && 
                   oData.travelStyleIndex >= 0;
        },

        _formatDate: function (oDate) {
            if (!oDate) return "";
            var year = oDate.getFullYear();
            var month = String(oDate.getMonth() + 1).padStart(2, '0');
            var day = String(oDate.getDate()).padStart(2, '0');
            return year + '-' + month + '-' + day;
        },

        getRouter: function () {
            return this.getOwnerComponent().getRouter();
        }
    });
});
