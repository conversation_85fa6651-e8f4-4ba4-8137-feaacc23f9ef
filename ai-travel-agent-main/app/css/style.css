/* Global Styles */
.sapUiBody {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

/* Main Page Styles */
.travelPlannerPage .sapMPageHeader {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.travelPlannerPage .sapMPageHeader .sapMTitle {
    color: white;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

/* Header Section */
.headerSection {
    text-align: center;
    margin-bottom: 2rem;
    padding: 2rem 0;
}

.mainTitle {
    font-size: 3.5rem !important;
    font-weight: 700 !important;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.4);
    margin-bottom: 1rem !important;
}

.subtitle {
    font-size: 1.3rem !important;
    color: white !important;
    opacity: 0.95;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    margin-bottom: 2rem !important;
}

.statusStrip {
    background: linear-gradient(45deg, rgba(0, 255, 0, 0.2), rgba(0, 200, 0, 0.3)) !important;
    border: 2px solid rgba(0, 255, 0, 0.4) !important;
    border-radius: 15px !important;
    backdrop-filter: blur(10px);
}

/* Form Panel */
.travelFormPanel {
    background: rgba(255, 255, 255, 0.15) !important;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 20px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    margin-bottom: 2rem;
}

.travelFormPanel .sapMPanelHdr {
    background: transparent !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.travelFormPanel .sapMTitle {
    color: #ffd700 !important;
    font-size: 2.2rem !important;
    font-weight: bold !important;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

/* Form Inputs */
.customInput .sapMInputBaseInner {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 2px solid rgba(255, 255, 255, 0.3) !important;
    border-radius: 10px !important;
    color: white !important;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.customInput .sapMInputBaseInner:focus {
    border-color: #ffd700 !important;
    background: rgba(255, 255, 255, 0.2) !important;
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
}

.customInput .sapMInputBaseInner::placeholder {
    color: rgba(255, 255, 255, 0.7) !important;
}

/* Labels */
.sapMLabel {
    color: #ffd700 !important;
    font-weight: 600 !important;
    font-size: 1.1rem !important;
}

/* Travel Style Radio Buttons */
.travelStyleGroup .sapMRb {
    margin: 0.5rem !important;
}

.travelStyleGroup .sapMRbBLabel {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 2px solid rgba(255, 255, 255, 0.3) !important;
    border-radius: 10px !important;
    padding: 15px 20px !important;
    color: white !important;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    cursor: pointer;
}

.travelStyleGroup .sapMRb.sapMRbSel .sapMRbBLabel {
    background: rgba(255, 215, 0, 0.3) !important;
    border-color: #ffd700 !important;
    color: #ffd700 !important;
    font-weight: bold !important;
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
}

/* Generate Button */
.generateButton {
    width: 100% !important;
    margin-top: 2rem !important;
}

.generateButton .sapMBtnInner {
    background: linear-gradient(45deg, #ffd700, #ffed4e) !important;
    color: #333 !important;
    border: none !important;
    border-radius: 15px !important;
    font-size: 1.3rem !important;
    font-weight: bold !important;
    text-transform: uppercase;
    letter-spacing: 1px;
    padding: 18px !important;
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
    transition: all 0.3s ease;
}

.generateButton:hover .sapMBtnInner {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(255, 215, 0, 0.6);
    background: linear-gradient(45deg, #ffed4e, #ffd700) !important;
}

/* Features Panel */
.featuresPanel {
    background: rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 15px !important;
    margin-bottom: 2rem;
}

.featuresContainer {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    justify-content: space-between;
}

.featureBox {
    flex: 1;
    min-width: 250px;
    background: rgba(255, 255, 255, 0.1);
    padding: 1.5rem;
    border-radius: 15px;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.featureBox:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.featureBox .sapMTitle {
    color: #ffd700 !important;
    font-size: 1.4rem !important;
    margin-bottom: 1rem !important;
}

.featureBox .sapMText {
    color: white !important;
}

/* Endpoints Panel */
.endpointsPanel {
    background: rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 15px !important;
}

.endpointsPanel .sapMLink {
    color: #87ceeb !important;
    display: block;
    margin-bottom: 1rem;
    padding: 15px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    border-left: 4px solid #ffd700;
    text-decoration: none !important;
}

.endpointsPanel .sapMLink:hover {
    color: #ffd700 !important;
    text-decoration: underline !important;
}

/* Results Page Styles */
.resultsPage .sapMPageHeader {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.resultsPage .sapMPageHeader .sapMTitle {
    color: white;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

/* Trip Summary */
.tripSummaryPanel {
    background: rgba(255, 255, 255, 0.15) !important;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 20px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    margin-bottom: 2rem;
}

.tripTitle {
    color: #ffd700 !important;
    font-size: 2rem !important;
    text-align: center;
    margin-bottom: 1.5rem !important;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.summaryGrid {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    justify-content: space-around;
}

.summaryItem {
    background: rgba(0, 0, 0, 0.2);
    padding: 1rem;
    border-radius: 10px;
    border-left: 4px solid #ffd700;
    min-width: 150px;
    text-align: center;
}

.summaryItem .sapMLabel {
    color: #ffd700 !important;
    font-size: 0.9rem !important;
}

.summaryValue {
    color: white !important;
    font-size: 1.2rem !important;
    font-weight: bold !important;
}

/* Panels */
.itineraryPanel,
.placesPanel,
.foodsPanel,
.accommodationsPanel,
.budgetPanel {
    background: rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 15px !important;
    margin-bottom: 2rem;
}

/* Day Cards */
.dayCard {
    background: rgba(255, 255, 255, 0.1) !important;
    border-radius: 15px !important;
    margin-bottom: 1rem !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.dayHeader {
    background: rgba(0, 0, 0, 0.2);
    padding: 1rem;
    border-radius: 15px 15px 0 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.dayTitle {
    color: #ffd700 !important;
    font-size: 1.5rem !important;
}

.dayDate {
    color: white !important;
    opacity: 0.8;
}

.activityItem {
    padding: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.activityTime {
    color: #ffd700 !important;
    font-weight: bold !important;
    min-width: 100px;
}

.activityName {
    color: white !important;
    font-weight: bold !important;
}

.activityDescription {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* Weather Info */
.weatherInfo {
    background: rgba(0, 0, 0, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 0 0 15px 15px;
}

.weatherIcon {
    color: #ffd700 !important;
    margin-right: 0.5rem;
}

.weatherText {
    color: white !important;
}

/* Lists */
.placesList .sapMLIB,
.foodsList .sapMLIB,
.accommodationsList .sapMLIB,
.budgetList .sapMLIB {
    background: rgba(255, 255, 255, 0.1) !important;
    border-radius: 10px !important;
    margin-bottom: 0.5rem !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.placesList .sapMSLITitle,
.foodsList .sapMSLITitle,
.accommodationsList .sapMSLITitle,
.budgetList .sapMSLITitle {
    color: white !important;
    font-weight: bold !important;
}

.placesList .sapMSLIDescription,
.foodsList .sapMSLIDescription,
.accommodationsList .sapMSLIDescription {
    color: rgba(255, 255, 255, 0.8) !important;
}

.placesList .sapMSLIInfo,
.foodsList .sapMSLIInfo,
.accommodationsList .sapMSLIInfo,
.budgetList .sapMSLIInfo {
    color: #ffd700 !important;
    font-weight: bold !important;
}

/* Budget Total */
.totalBudget {
    background: rgba(0, 0, 0, 0.2);
    padding: 1rem;
    border-radius: 10px;
    border: 2px solid #ffd700;
    margin-top: 1rem;
}

.totalLabel {
    color: white !important;
    font-size: 1.2rem !important;
    font-weight: bold !important;
}

.totalAmount {
    color: #ffd700 !important;
    font-size: 1.5rem !important;
    font-weight: bold !important;
}

/* Footer */
.sapMPageFooter {
    background: rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(15px);
    border-top: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.sapMPageFooter .sapMText {
    color: white !important;
    opacity: 0.8;
}

/* Responsive Design */
@media (max-width: 768px) {
    .mainTitle {
        font-size: 2.5rem !important;
    }
    
    .featuresContainer {
        flex-direction: column;
    }
    
    .summaryGrid {
        flex-direction: column;
    }
    
    .summaryItem {
        min-width: auto;
    }
}
