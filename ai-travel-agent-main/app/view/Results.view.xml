<mvc:View
    controllerName="ai.travel.planner.controller.Results"
    xmlns:mvc="sap.ui.core.mvc"
    xmlns:core="sap.ui.core"
    xmlns="sap.m">
    
    <Page id="resultsPage" title="Your AI Travel Plan" class="resultsPage" showNavButton="true" navButtonPress="onNavBack">
        <content>
            <VBox class="sapUiMediumMargin">
                
                <!-- Trip Summary -->
                <Panel headerText="🎉 Your Perfect Travel Plan" class="tripSummaryPanel">
                    <content>
                        <VBox class="sapUiMediumMargin">
                            <Title text="{/tripTitle}" level="H2" class="tripTitle"/>
                            
                            <HBox class="summaryGrid">
                                <VBox class="summaryItem">
                                    <Label text="📍 Destination"/>
                                    <Text text="{/destination}" class="summaryValue"/>
                                </VBox>
                                <VBox class="summaryItem">
                                    <Label text="📅 Duration"/>
                                    <Text text="{/duration}" class="summaryValue"/>
                                </VBox>
                                <VBox class="summaryItem">
                                    <Label text="💰 Budget"/>
                                    <Text text="₹{/budget}" class="summaryValue"/>
                                </VBox>
                                <VBox class="summaryItem">
                                    <Label text="👥 Travelers"/>
                                    <Text text="{/travelers}" class="summaryValue"/>
                                </VBox>
                            </HBox>
                        </VBox>
                    </content>
                </Panel>
                
                <!-- Itinerary -->
                <Panel headerText="📋 Day-by-Day Itinerary" class="itineraryPanel">
                    <content>
                        <List items="{/itinerary}" class="itineraryList">
                            <CustomListItem class="dayCard">
                                <content>
                                    <VBox class="dayContent">
                                        <HBox class="dayHeader">
                                            <Title text="{day}" level="H3" class="dayTitle"/>
                                            <ToolbarSpacer/>
                                            <Text text="{date}" class="dayDate"/>
                                        </HBox>
                                        
                                        <VBox class="activitiesContainer">
                                            <List items="{activities}" class="activitiesList">
                                                <CustomListItem>
                                                    <content>
                                                        <HBox class="activityItem">
                                                            <Text text="{time}" class="activityTime"/>
                                                            <VBox class="activityDetails">
                                                                <Text text="{activity}" class="activityName"/>
                                                                <Text text="{description}" class="activityDescription"/>
                                                            </VBox>
                                                        </HBox>
                                                    </content>
                                                </CustomListItem>
                                            </List>
                                        </VBox>
                                        
                                        <!-- Weather Info -->
                                        <HBox class="weatherInfo" visible="{= ${weather} !== undefined}">
                                            <Icon src="sap-icon://weather-proofing" class="weatherIcon"/>
                                            <Text text="Weather: {weather/description}, {weather/temp}°C" class="weatherText"/>
                                        </HBox>
                                    </VBox>
                                </content>
                            </CustomListItem>
                        </List>
                    </content>
                </Panel>
                
                <!-- Must Visit Places -->
                <Panel headerText="🏛️ Must-Visit Places" class="placesPanel">
                    <content>
                        <List items="{/mustVisitPlaces}" class="placesList">
                            <StandardListItem 
                                title="{name}"
                                description="{description}"
                                info="{category}"
                                class="placeItem"/>
                        </List>
                    </content>
                </Panel>
                
                <!-- Local Foods -->
                <Panel headerText="🍽️ Local Foods to Try" class="foodsPanel">
                    <content>
                        <List items="{/localFoods}" class="foodsList">
                            <StandardListItem 
                                title="{name}"
                                description="{description}"
                                info="₹{price_range}"
                                class="foodItem"/>
                        </List>
                    </content>
                </Panel>
                
                <!-- Accommodations -->
                <Panel headerText="🏨 Recommended Accommodations" class="accommodationsPanel">
                    <content>
                        <List items="{/accommodations}" class="accommodationsList">
                            <StandardListItem 
                                title="{name}"
                                description="{address} - {reason}"
                                info="₹{estimated_price_per_night}/night"
                                class="accommodationItem"/>
                        </List>
                    </content>
                </Panel>
                
                <!-- Budget Breakdown -->
                <Panel headerText="💰 Budget Breakdown" class="budgetPanel">
                    <content>
                        <VBox class="sapUiMediumMargin">
                            <List items="{/budgetBreakdown}" class="budgetList">
                                <StandardListItem 
                                    title="{category}"
                                    info="₹{amount}"
                                    class="budgetItem"/>
                            </List>
                            <HBox class="totalBudget">
                                <Text text="Total Estimated Cost:" class="totalLabel"/>
                                <ToolbarSpacer/>
                                <Text text="₹{/totalBudget}" class="totalAmount"/>
                            </HBox>
                        </VBox>
                    </content>
                </Panel>
                
            </VBox>
        </content>
        
        <footer>
            <Toolbar>
                <Button text="📤 Export Plan" press="onExportPlan" type="Emphasized"/>
                <ToolbarSpacer/>
                <Button text="🔄 Plan Another Trip" press="onPlanAnother"/>
            </Toolbar>
        </footer>
    </Page>
    
</mvc:View>
