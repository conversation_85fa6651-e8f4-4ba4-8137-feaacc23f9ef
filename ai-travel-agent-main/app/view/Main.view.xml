<mvc:View
    controllerName="ai.travel.planner.controller.Main"
    xmlns:mvc="sap.ui.core.mvc"
    xmlns:core="sap.ui.core"
    xmlns="sap.m">
    
    <Page id="page" title="AI Travel Planner" class="travelPlannerPage">
        <content>
            <VBox class="sapUiMediumMargin">
                
                <!-- Header Section -->
                <VBox class="headerSection">
                    <Title text="🌟 AI Travel Planner" class="mainTitle"/>
                    <Text text="Your Intelligent Travel Companion for Exploring India" class="subtitle"/>
                    <MessageStrip text="🚀 System Status: ONLINE | Database Connected | AI Ready" 
                                  type="Success" class="statusStrip"/>
                </VBox>
                
                <!-- Travel Planning Form -->
                <Panel headerText="✈️ Plan Your Perfect Trip" class="travelFormPanel">
                    <content>
                        <VBox class="sapUiMediumMargin">
                            
                            <!-- Form Grid -->
                            <SimpleForm
                                editable="true"
                                layout="ResponsiveGridLayout"
                                labelSpanXL="3"
                                labelSpanL="3"
                                labelSpanM="3"
                                labelSpanS="12"
                                adjustLabelSpan="false"
                                emptySpanXL="4"
                                emptySpanL="4"
                                emptySpanM="4"
                                emptySpanS="0"
                                columnsXL="1"
                                columnsL="1"
                                columnsM="1"
                                singleContainerFullSize="false">
                                
                                <content>
                                    <Label text="🏠 Origin City *" required="true"/>
                                    <Input id="originInput" 
                                           value="{/origin}" 
                                           placeholder="e.g., Chennai, Mumbai, Delhi"
                                           required="true"
                                           class="customInput"/>
                                    
                                    <Label text="🎯 Destination *" required="true"/>
                                    <Input id="destinationInput" 
                                           value="{/destination}" 
                                           placeholder="e.g., Manali, Goa, Kerala"
                                           required="true"
                                           class="customInput"/>
                                    
                                    <Label text="📅 Start Date *" required="true"/>
                                    <DatePicker id="startDatePicker" 
                                               value="{/startDate}"
                                               required="true"
                                               class="customInput"/>
                                    
                                    <Label text="📅 End Date *" required="true"/>
                                    <DatePicker id="endDatePicker" 
                                               value="{/endDate}"
                                               required="true"
                                               class="customInput"/>
                                    
                                    <Label text="💰 Budget (₹) *" required="true"/>
                                    <Input id="budgetInput" 
                                           value="{/budget}" 
                                           type="Number"
                                           placeholder="e.g., 30000"
                                           required="true"
                                           class="customInput"/>
                                    
                                    <Label text="👥 Number of Travelers *" required="true"/>
                                    <Select id="travelersSelect" 
                                            selectedKey="{/travelers}"
                                            required="true"
                                            class="customInput">
                                        <core:Item key="1" text="1 Traveler"/>
                                        <core:Item key="2" text="2 Travelers"/>
                                        <core:Item key="3" text="3 Travelers"/>
                                        <core:Item key="4" text="4 Travelers"/>
                                        <core:Item key="5" text="5+ Travelers"/>
                                    </Select>
                                    
                                    <Label text="🎨 Travel Style *" required="true"/>
                                    <RadioButtonGroup id="travelStyleGroup" 
                                                     selectedIndex="{/travelStyleIndex}"
                                                     columns="2"
                                                     class="travelStyleGroup">
                                        <RadioButton text="💸 Budget" />
                                        <RadioButton text="🧘 Relaxation" />
                                        <RadioButton text="🏔️ Adventure" />
                                        <RadioButton text="🏛️ Cultural" />
                                    </RadioButtonGroup>
                                </content>
                            </SimpleForm>
                            
                            <!-- Generate Button -->
                            <Button text="✨ Generate AI Travel Plan" 
                                    type="Emphasized" 
                                    press="onGeneratePlan"
                                    class="generateButton"/>
                        </VBox>
                    </content>
                </Panel>
                
                <!-- Features Section -->
                <Panel headerText="🌟 Features" class="featuresPanel">
                    <content>
                        <HBox class="featuresContainer">
                            <VBox class="featureBox">
                                <Title text="🤖 AI-Powered Planning" level="H4"/>
                                <Text text="Get personalized itineraries generated by advanced AI technology"/>
                            </VBox>
                            <VBox class="featureBox">
                                <Title text="🌤️ Real-time Weather" level="H4"/>
                                <Text text="Weather forecasts integrated into your travel plans"/>
                            </VBox>
                            <VBox class="featureBox">
                                <Title text="📸 Beautiful Photos" level="H4"/>
                                <Text text="High-quality destination photos from Pexels API"/>
                            </VBox>
                            <VBox class="featureBox">
                                <Title text="🗺️ Smart Recommendations" level="H4"/>
                                <Text text="Curated suggestions for places, food, and accommodations"/>
                            </VBox>
                        </HBox>
                    </content>
                </Panel>
                
                <!-- API Endpoints Section -->
                <Panel headerText="🔗 Available API Endpoints" class="endpointsPanel">
                    <content>
                        <VBox class="sapUiSmallMargin">
                            <Link text="Service Metadata: /service/plan/$metadata" 
                                  href="/service/plan/$metadata" 
                                  target="_blank"/>
                            <Link text="Service Document: /service/plan/" 
                                  href="/service/plan/" 
                                  target="_blank"/>
                            <Link text="Travel Plans: /service/plan/TravelPlans" 
                                  href="/service/plan/TravelPlans" 
                                  target="_blank"/>
                            <Link text="Health Check: /service/plan/health" 
                                  href="/service/plan/health" 
                                  target="_blank"/>
                        </VBox>
                    </content>
                </Panel>
                
            </VBox>
        </content>
        
        <footer>
            <Toolbar>
                <ToolbarSpacer/>
                <Text text="🚀 Deployed on SAP BTP Cloud Foundry | Powered by Google Gemini AI"/>
                <ToolbarSpacer/>
            </Toolbar>
        </footer>
    </Page>
    
</mvc:View>
