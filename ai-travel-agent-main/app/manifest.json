{"_version": "1.58.0", "sap.app": {"id": "ai.travel.planner", "type": "application", "i18n": "i18n/i18n.properties", "applicationVersion": {"version": "1.0.0"}, "title": "AI Travel Planner", "description": "Intelligent Travel Planning Application", "resources": "resources.json", "sourceTemplate": {"id": "@sap/generator-fiori:basic", "version": "1.11.4", "toolsId": "b0b8e148-cd4b-4306-a7b8-b0b8e148cd4b"}, "dataSources": {"mainService": {"uri": "/service/plan/", "type": "OData", "settings": {"annotations": [], "localUri": "localService/metadata.xml", "odataVersion": "4.0"}}}}, "sap.ui": {"technology": "UI5", "icons": {"icon": "", "favIcon": "", "phone": "", "phone@2": "", "tablet": "", "tablet@2": ""}, "deviceTypes": {"desktop": true, "tablet": true, "phone": true}}, "sap.ui5": {"flexEnabled": true, "dependencies": {"minUI5Version": "1.120.0", "libs": {"sap.m": {}, "sap.ui.core": {}, "sap.f": {}, "sap.suite.ui.commons": {}}}, "models": {"i18n": {"dataSource": "i18n", "type": "sap.ui.model.resource.ResourceModel", "settings": {"bundleName": "ai.travel.planner.i18n.i18n"}}, "": {"dataSource": "mainService", "type": "sap.ui.model.odata.v4.ODataModel", "settings": {"operationMode": "Server", "autoExpandSelect": true, "earlyRequests": true}}}, "resources": {"css": [{"uri": "css/style.css"}]}, "routing": {"config": {"routerClass": "sap.m.routing.Router", "viewType": "XML", "async": true, "viewPath": "ai.travel.planner.view", "controlAggregation": "pages", "controlId": "app", "clearControlAggregation": false}, "routes": [{"name": "RouteMain", "pattern": ":?query:", "target": ["TargetMain"]}, {"name": "RouteResults", "pattern": "results:?query:", "target": ["TargetResults"]}], "targets": {"TargetMain": {"viewType": "XML", "transition": "slide", "clearControlAggregation": false, "viewId": "Main", "viewName": "Main"}, "TargetResults": {"viewType": "XML", "transition": "slide", "clearControlAggregation": false, "viewId": "Results", "viewName": "Results"}}}, "rootView": {"viewName": "ai.travel.planner.view.App", "type": "XML", "async": true, "id": "App"}}, "sap.cloud": {"public": true, "service": "ai.travel.planner"}}