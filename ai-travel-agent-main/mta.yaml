_schema-version: "3.1"
ID: ai-travel-planner
description: AI Travel Planner - Intelligent Travel Planning for India
version: 1.0.0
modules:
  - name: ai-travel-planner-srv
    type: nodejs
    path: gen/srv
    provides:
      - name: srv-api
        properties:
          srv-url: ${default-url}
    parameters:
      buildpack: nodejs_buildpack
      readiness-health-check-http-endpoint: /service/plan/health
      readiness-health-check-type: http
    build-parameters:
      builder: npm-ci
    requires:
      - name: ai-travel-hana-db
      - name: ai-travel-xsuaa
      - name: ai-travel-destination
      - name: ai-travel-connectivity
    properties:
      GEMINI_API_KEY: ~{ai-travel-destination/GEMINI_API_KEY}
      OPENWEATHER_API_KEY: ~{ai-travel-destination/OPENWEATHER_API_KEY}
      PEXELS_API_KEY: ~{ai-travel-destination/PEXELS_API_KEY}
      GOOGLE_MAPS_API_KEY: ~{ai-travel-destination/GOOGLE_MAPS_API_KEY}

  - name: ai-travel-planner-db-deployer
    type: hdb
    path: gen/db
    requires:
      - name: ai-travel-hana-db
    parameters:
      buildpack: nodejs_buildpack

  - name: ai-travel-planner-webapp
    type: html5
    path: app/webapp
    build-parameters:
      build-result: .
      builder: custom
      commands: []
      supported-platforms: []

resources:
  - name: ai-travel-hana-db
    type: com.sap.xs.hdi-container
    parameters:
      service: hana
      service-plan: hdi-shared
    properties:
      hdi-service-name: ${service-name}

  - name: ai-travel-xsuaa
    type: org.cloudfoundry.managed-service
    parameters:
      service: xsuaa
      service-plan: application
      path: ./xs-security.json
      config:
        xsappname: ai-travel-planner-${org}-${space}
        tenant-mode: dedicated
        description: Security profile for AI Travel Planner
        scopes:
          - name: "$XSAPPNAME.Callback"
            description: "With this scope set, the callbacks for tenant onboarding, offboarding and getDependencies can be called."
            grant-as-authority-to-apps:
              - "$XSAPPNAME(application,sap-provisioning,tenant-onboarding)"
        role-templates:
          - name: "Token_Exchange"
            description: "UAA"
            scope-references:
              - "$XSAPPNAME.Callback"

  - name: ai-travel-destination
    type: org.cloudfoundry.managed-service
    parameters:
      service: destination
      service-plan: lite
      config:
        HTML5Runtime_enabled: false
        init_data:
          subaccount:
            destinations:
              - Name: ai-travel-gemini-api
                Description: Google Gemini AI API
                URL: https://generativelanguage.googleapis.com
                Type: REST
                ProxyType: Internet
                Authentication: NoAuthentication
                HTML5.DynamicDestination: false
              - Name: ai-travel-weather-api
                Description: OpenWeatherMap API
                URL: https://api.openweathermap.org
                Type: REST
                ProxyType: Internet
                Authentication: NoAuthentication
                HTML5.DynamicDestination: false
              - Name: ai-travel-photos-api
                Description: Pexels Photos API
                URL: https://api.pexels.com
                Type: REST
                ProxyType: Internet
                Authentication: NoAuthentication
                HTML5.DynamicDestination: false
            existing_destinations_policy: update
        version: 1.0.0
    properties:
      GEMINI_API_KEY: "AIzaSyC8yBMwSj3ipoM03yWPNyLYDUMcsbLKN4k"
      OPENWEATHER_API_KEY: "********************************"
      PEXELS_API_KEY: "nKIYWb5f4GUSzQiajOGKSf2xioILhJovnI30osFk68ESGH03qkvp0PR9"
      GOOGLE_MAPS_API_KEY: "AIzaSyCDjLDfKkq-t4dynUX2lUPH-lPcMP6PBSg"

  - name: ai-travel-connectivity
    type: org.cloudfoundry.managed-service
    parameters:
      service: connectivity
      service-plan: lite

parameters:
  enable-parallel-deployments: true
build-parameters:
  before-all:
    - builder: custom
      commands:
        - npm install
        - npm run build