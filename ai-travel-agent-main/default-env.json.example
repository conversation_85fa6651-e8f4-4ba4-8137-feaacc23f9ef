{"VCAP_SERVICES": {"hana": [{"label": "hana", "provider": null, "plan": "hdi-shared", "name": "ai-travel-hana-db", "tags": ["hana", "database", "relational"], "instance_name": "ai-travel-hana-db", "binding_name": null, "credentials": {"host": "your-hana-host.hanacloud.ondemand.com", "port": "443", "driver": "com.sap.db.jdbc.Driver", "url": "*******************************************************************************************", "schema": "YOUR_SCHEMA", "hdi_user": "YOUR_HDI_USER", "hdi_password": "YOUR_HDI_PASSWORD", "user": "YOUR_USER", "password": "YOUR_PASSWORD", "certificate": "-----BEGIN CERTIFICATE-----\n...\n-----END CERTIFICATE-----"}}], "xsuaa": [{"label": "xsuaa", "provider": null, "plan": "application", "name": "ai-travel-xsuaa", "tags": ["xsuaa"], "instance_name": "ai-travel-xsuaa", "binding_name": null, "credentials": {"tenantmode": "dedicated", "sburl": "https://internal-xsuaa.authentication.sap.hana.ondemand.com", "clientid": "your-client-id", "clientsecret": "your-client-secret", "url": "https://your-subdomain.authentication.sap.hana.ondemand.com", "uaadomain": "authentication.sap.hana.ondemand.com", "verificationkey": "-----BEGIN PUBLIC KEY-----\n...\n-----END PUBLIC KEY-----", "xsappname": "ai-travel-planner-your-org-your-space", "identityzone": "your-subdomain", "identityzoneid": "your-zone-id", "tenantid": "your-tenant-id"}}]}}