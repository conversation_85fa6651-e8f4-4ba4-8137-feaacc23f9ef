# 🌏 Bharat-AI Travel Agent

An intelligent travel planning application for India, powered by Google Gemini AI and built with SAP CAP and UI5.

## 🚀 Features

- **AI-Powered Planning**: Uses Google Gemini AI to generate personalized travel itineraries
- **Cultural Insights**: Provides authentic Indian travel experiences and local recommendations
- **Budget Optimization**: Smart budget allocation and cost breakdown
- **Interactive UI**: Modern UI5 interface with real-time validation
- **Export Functionality**: Download travel plans as JSON files

## 🛠️ Technology Stack

- **Backend**: SAP CAP (Cloud Application Programming Model)
- **Frontend**: SAPUI5/OpenUI5
- **AI**: Google Gemini Pro
- **Database**: SQLite (development)
- **Runtime**: Node.js

## 📋 Prerequisites

- Node.js 18+ 
- npm 8+
- Google Gemini API Key

## 🚀 Quick Start

### 1. Clone and Install

```bash
git clone <repository-url>
cd travel-agent-prototype
npm install
```

### 2. Configure Environment

Copy the environment template and add your API key:

```bash
cp env.example .env
```

Edit `.env` and add your Gemini API key:
```
GEMINI_API_KEY=your-actual-api-key-here
```

### 3. Start the Application

```bash
npm start
```

The application will be available at:
- **UI**: http://localhost:4004/webapp/
- **API**: http://localhost:4004/v2/travel/

## 📖 Usage

1. **Enter Trip Details**: Fill in origin, destination, dates, budget, and number of travelers
2. **Generate Plan**: Click "Generate Plan" to get an AI-generated itinerary
3. **Review Results**: View detailed daily plans, accommodation, and budget breakdown
4. **Export Plan**: Download your travel plan as a JSON file

## 🔧 API Endpoints

- `GET /service/plan/health` - Health check
- `POST /service/plan/generatePlan` - Generate travel plan
- `GET /v2/travel/$metadata` - OData V2 Service Metadata

## 📁 Project Structure

```
travel-agent-prototype/
├── app/webapp/           # UI5 Frontend
│   ├── controller/       # UI5 Controllers
│   ├── view/            # UI5 Views
│   ├── model/           # Data Models
│   └── manifest.json    # UI5 App Descriptor
├── srv/                 # CAP Services
│   ├── plan-service.js  # Main Service Implementation
│   └── travel-service.cds # Service Definition
├── db/                  # Database Models
│   └── models/schema.cds
└── package.json         # Dependencies
```

## 🔑 Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `GEMINI_API_KEY` | Google Gemini API Key | Yes |

## 🎯 Key Features

- **Form Validation**: Real-time input validation with visual feedback
- **Responsive Design**: Works on desktop and mobile devices
- **Error Handling**: Comprehensive error handling and user feedback
- **Export Functionality**: Download travel plans for offline use
- **Budget Tracking**: Detailed cost breakdown and budget analysis

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For issues and questions, please create an issue in the repository.

---

**Built with ❤️ for Indian Travel**
