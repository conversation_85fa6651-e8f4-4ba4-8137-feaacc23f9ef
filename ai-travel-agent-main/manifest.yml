---
applications:
- name: ai-travel-planner
  memory: 1024M
  disk_quota: 1024M
  instances: 1
  buildpacks:
    - nodejs_buildpack
  path: .
  command: npm start
  env:
    NODE_ENV: production
    CDS_ENV: production
  services:
    - ai-travel-hana-db
    - ai-travel-destination
    - ai-travel-connectivity
    - ai-travel-xsuaa
  routes:
    - route: ai-travel-planner-[random-word].cfapps.sap.hana.ondemand.com
