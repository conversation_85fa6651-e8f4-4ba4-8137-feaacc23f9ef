# Dependencies (NEVER commit - they're huge!)
node_modules/
package-lock.json
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build outputs
gen/
dist/
build/
*.tgz
*.tar.gz

# Database
*.db
*.sqlite

# Logs
logs/
*.log

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# SAP specific
mta_archives/
.mta/
*.mtar

# Backup files
backup/
*.bak
*.backup

# Temporary files
tmp/
temp/
*.tmp

# UI5 specific
app/travel-ui/dist/
app/travel-ui/node_modules/

# CAP specific
_out/
default-*.json
*.cov
